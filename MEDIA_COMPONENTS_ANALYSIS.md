# Análisis de Arquitectura Actual - Componentes de Medios

## Componentes Actuales Identificados

### 1. **ImageUpload** (Slideshow)
- **Ubicación**: `frontend/src/slideshow/components/ImageUpload.tsx`
- **Responsabilidades**:
  - Drag & drop de imágenes
  - Lista vertical de imágenes subidas
  - Thumbnails con hover effects
  - Botón de agregar a timeline
  - Botón de eliminar con zona separada
- **Problemas**:
  - Lógica de UI mezclada con lógica de negocio
  - Estilos inline hardcodeados
  - No reutilizable fuera del contexto slideshow

### 2. **VideoUploader** (Video Editor)
- **Ubicación**: `frontend/src/video-editor/components/VideoUploader.tsx`
- **Responsabilidades**:
  - Drag & drop de videos
  - Área de upload con estados
  - Información del video subido
  - Botón de reemplazar video
- **Problemas**:
  - Funcionalidad similar a ImageUpload pero completamente separada
  - No hay reutilización de componentes de UI

### 3. **Timeline Components**
- **Ubicaciones**:
  - `frontend/src/slideshow/components/Timeline.tsx`
  - `frontend/src/slideshow/components/TimelineItem.tsx`
  - `frontend/src/components/Timeline/Timeline.tsx`
- **Responsabilidades**:
  - Mostrar medios en secuencia horizontal
  - Drag & drop para reordenar
  - Thumbnails con información de duración
  - Controles de transición
- **Problemas**:
  - Múltiples implementaciones de Timeline
  - Código duplicado para drag & drop
  - Estilos inconsistentes entre versiones

### 4. **TimelineThumbnails** (Video Editor)
- **Ubicación**: `frontend/src/video-editor/components/timeline/TimelineThumbnails.tsx`
- **Responsabilidades**:
  - Mostrar thumbnails de video en timeline
  - Cálculo de ancho de thumbnails
- **Problemas**:
  - Específico solo para videos
  - No reutilizable para imágenes

## Patrones de Duplicación Identificados

### 1. **Drag & Drop Logic**
```typescript
// Patrón repetido en múltiples componentes:
const handleDrag = (e: React.DragEvent) => {
  e.preventDefault();
  e.stopPropagation();
  if (e.type === 'dragenter' || e.type === 'dragover') {
    setDragActive(true);
  } else if (e.type === 'dragleave') {
    setDragActive(false);
  }
};
```

### 2. **Thumbnail Rendering**
```typescript
// Patrón repetido para mostrar thumbnails:
<img
  src={item.preview}
  alt="Media"
  className="w-full h-full object-cover"
/>
```

### 3. **Hover Effects**
```typescript
// Lógica de hover repetida:
onMouseEnter={(e) => {
  e.currentTarget.style.backgroundColor = '#343536';
  e.currentTarget.style.borderColor = '#ff4500';
}}
```

### 4. **File Upload Handling**
```typescript
// Lógica similar de manejo de archivos:
const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
  if (e.target.files && e.target.files.length > 0) {
    const files = Array.from(e.target.files);
    await uploadFiles(files);
  }
};
```

## Estructura de Datos Inconsistente

### MediaItem (Context)
```typescript
interface MediaItem {
  id: string;
  file: File;
  filename: string;
  originalName: string;
  path: string;
  name: string;
  type: string;
  size: number;
  duration: number;
  transition: TransitionType;
}
```

### Image (Slideshow)
```typescript
// Estructura diferente en slideshow
interface Image {
  id: string;
  name: string;
  preview: string;
  file: File;
  uploadedInfo?: any;
}
```

### VideoFile (Video Editor)
```typescript
interface VideoFile {
  file: File;
  id: string;
  name: string;
  duration: number;
  fps?: number;
  width: number;
  height: number;
  size: number;
  thumbnails: string[];
  uploadedInfo?: UploadedVideoInfo;
}
```

## Oportunidades de Refactorización

### 1. **Componentes Base Reutilizables**
- `MediaItem`: Componente base para mostrar cualquier tipo de media
- `MediaThumbnail`: Componente especializado para thumbnails
- `MediaList`: Lista vertical de medios
- `MediaGrid`: Grid de medios
- `DropZone`: Área de drag & drop reutilizable

### 2. **Hooks Personalizados**
- `useMediaDragDrop`: Lógica de drag & drop
- `useMediaSelection`: Manejo de selección múltiple
- `useMediaActions`: Acciones comunes (add, remove, reorder)
- `useMediaUpload`: Lógica de subida de archivos

### 3. **Sistema de Temas**
- Colores consistentes
- Espaciado estandarizado
- Transiciones uniformes
- Tipografía coherente

### 4. **Tipos Unificados**
```typescript
interface BaseMediaItem {
  id: string;
  file: File;
  name: string;
  type: 'image' | 'video';
  size: number;
  preview?: string;
  uploadedInfo?: UploadedInfo;
}

interface ImageMediaItem extends BaseMediaItem {
  type: 'image';
}

interface VideoMediaItem extends BaseMediaItem {
  type: 'video';
  duration: number;
  width: number;
  height: number;
  fps?: number;
  thumbnails: string[];
}
```

## Problemas de Rendimiento Identificados

### 1. **Falta de Virtualización**
- Listas largas de medios no están virtualizadas
- Todos los thumbnails se renderizan simultáneamente
- No hay lazy loading de imágenes

### 2. **Re-renders Innecesarios**
- Componentes no están memoizados
- Funciones se recrean en cada render
- Estados locales causan re-renders de toda la lista

### 3. **Gestión de Memoria**
- Previews de imágenes no se liberan
- Event listeners no se limpian correctamente
- Referencias a archivos se mantienen indefinidamente

## Inconsistencias de UX

### 1. **Interacciones Diferentes**
- ImageUpload: Click para agregar a timeline
- VideoUploader: No tiene timeline integration
- Timeline: Drag para reordenar

### 2. **Estados Visuales**
- Diferentes colores de hover
- Inconsistencia en bordes y sombras
- Diferentes tamaños de thumbnails

### 3. **Feedback al Usuario**
- Algunos componentes muestran loading states
- Otros no proporcionan feedback visual
- Mensajes de error inconsistentes

## Recomendaciones de Arquitectura

### 1. **Jerarquía de Componentes Propuesta**
```
MediaComponents/
├── Base/
│   ├── MediaItem.tsx          # Componente base
│   ├── MediaThumbnail.tsx     # Thumbnail reutilizable
│   ├── DropZone.tsx           # Área de drag & drop
│   └── LoadingState.tsx       # Estados de carga
├── Lists/
│   ├── MediaList.tsx          # Lista vertical
│   ├── MediaGrid.tsx          # Grid de medios
│   └── VirtualizedList.tsx    # Lista virtualizada
├── Upload/
│   ├── MediaUploader.tsx      # Uploader genérico
│   └── FileSelector.tsx       # Selector de archivos
└── Timeline/
    ├── TimelineTrack.tsx      # Track de timeline
    └── TimelineItem.tsx       # Item de timeline
```

### 2. **Hooks Especializados**
```
hooks/
├── useMediaDragDrop.ts        # Drag & drop logic
├── useMediaSelection.ts       # Selección múltiple
├── useMediaActions.ts         # Acciones CRUD
├── useMediaUpload.ts          # Upload logic
└── useVirtualization.ts       # Virtualización
```

### 3. **Sistema de Temas**
```
theme/
├── mediaTheme.ts              # Tema para componentes
├── animations.ts              # Animaciones consistentes
└── breakpoints.ts             # Responsive design
```

Esta arquitectura permitirá:
- Reutilización máxima de componentes
- Consistencia visual y de UX
- Mejor rendimiento con virtualización
- Mantenibilidad mejorada
- Extensibilidad para nuevos tipos de media
