#!/bin/bash

# 🚀 AnimaGen Backend Deployment Script
# Este script despliega el backend para producción

echo "🚀 Desplegando AnimaGen Backend..."

# Verificar que estamos en el directorio correcto
if [ ! -f "backend/package.json" ]; then
    echo "❌ Error: No se encontró el package.json del backend. Asegúrate de estar en el directorio raíz del proyecto."
    exit 1
fi

# Verificar que las variables de entorno estén configuradas
if [ -z "$RAILWAY_TOKEN" ]; then
    echo "⚠️ Advertencia: RAILWAY_TOKEN no está configurado."
    echo "   Para desplegar en Railway, configura la variable RAILWAY_TOKEN"
fi

# Instalar dependencias del backend
echo "📦 Instalando dependencias del backend..."
cd backend
npm install --production

if [ $? -ne 0 ]; then
    echo "❌ Error: No se pudieron instalar las dependencias."
    exit 1
fi

# Verificar que FFmpeg esté disponible
echo "🔍 Verificando FFmpeg..."
if ! command -v ffmpeg &> /dev/null; then
    echo "⚠️ Advertencia: FFmpeg no está instalado en el sistema."
    echo "   El backend funcionará pero los exports de video fallarán."
else
    echo "✅ FFmpeg encontrado: $(ffmpeg -version | head -n1)"
fi

# Crear archivo de configuración de producción
echo "⚙️ Creando configuración de producción..."
cat > .env.production << EOF
NODE_ENV=production
PORT=3001
CORS_ORIGINS=*
OUTPUT_DIR=output
TEMP_DIR=uploads
LOG_LEVEL=info
EOF

echo "✅ Configuración de producción creada"

# Opciones de despliegue
echo ""
echo "🎯 Opciones de despliegue:"
echo "1. Railway (Recomendado)"
echo "2. Heroku"
echo "3. Vercel"
echo "4. Local con PM2"
echo "5. Docker"

read -p "Selecciona una opción (1-5): " choice

case $choice in
    1)
        echo "🚂 Desplegando en Railway..."
        if [ -z "$RAILWAY_TOKEN" ]; then
            echo "❌ Error: RAILWAY_TOKEN no está configurado."
            echo "   Ejecuta: export RAILWAY_TOKEN=tu_token"
            exit 1
        fi
        railway login
        railway up
        ;;
    2)
        echo "🦊 Desplegando en Heroku..."
        if ! command -v heroku &> /dev/null; then
            echo "❌ Error: Heroku CLI no está instalado."
            echo "   Instala Heroku CLI primero: https://devcenter.heroku.com/articles/heroku-cli"
            exit 1
        fi
        heroku create animagen-backend
        git add .
        git commit -m "Deploy to Heroku"
        git push heroku main
        ;;
    3)
        echo "▲ Desplegando en Vercel..."
        if ! command -v vercel &> /dev/null; then
            echo "❌ Error: Vercel CLI no está instalado."
            echo "   Instala Vercel CLI: npm i -g vercel"
            exit 1
        fi
        vercel --prod
        ;;
    4)
        echo "🔄 Desplegando localmente con PM2..."
        if ! command -v pm2 &> /dev/null; then
            echo "❌ Error: PM2 no está instalado."
            echo "   Instala PM2: npm install -g pm2"
            exit 1
        fi
        pm2 start index.js --name "animagen-backend"
        pm2 save
        pm2 startup
        ;;
    5)
        echo "🐳 Desplegando con Docker..."
        if ! command -v docker &> /dev/null; then
            echo "❌ Error: Docker no está instalado."
            exit 1
        fi
        docker build -t animagen-backend .
        docker run -d -p 3001:3001 --name animagen-backend animagen-backend
        ;;
    *)
        echo "❌ Opción inválida."
        exit 1
        ;;
esac

echo ""
echo "✅ Despliegue completado!"
echo ""
echo "📋 Próximos pasos:"
echo "1. Verifica que el backend esté funcionando"
echo "2. Actualiza la URL del backend en el plugin"
echo "3. Distribuye el plugin a los beta testers"
echo "4. Monitorea los logs para detectar problemas"
echo ""
echo "🔗 URLs útiles:"
echo "- Health check: http://tu-dominio/api/health"
echo "- Status: http://tu-dominio/api/status"
echo ""
echo "🎉 ¡Backend listo para beta testing!" 