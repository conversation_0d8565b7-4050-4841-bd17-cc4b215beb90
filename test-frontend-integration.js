#!/usr/bin/env node

/**
 * Test script para probar la integración completa del nuevo sistema
 * Preview -> Export con el frontend de AnimaGen
 */

const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

// Helper function to make HTTP requests
async function makeRequest(method, endpoint, data = null) {
  const url = `${API_BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return {
      status: response.status,
      data: result,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      data: { error: error.message },
      ok: false
    };
  }
}

async function testFrontendIntegration() {
  console.log('🧪 Testing Frontend Integration - New Preview -> Export System');
  console.log('================================================================\n');

  const sessionId = `frontend_test_${Date.now()}`;
  
  console.log(`📋 Test Session: ${sessionId}`);
  console.log(`🌐 Frontend URL: http://localhost:5181/slideshow`);
  console.log(`🔧 Backend URL: ${API_BASE_URL}\n`);

  // Test 1: Verify backend is running
  console.log('🔍 Step 1: Verifying backend connection...');
  const statusResponse = await makeRequest('GET', '/api/status');
  
  if (!statusResponse.ok) {
    console.log('❌ Backend not accessible!');
    console.log('   Make sure backend is running: cd backend && npm start');
    return;
  }
  
  console.log('✅ Backend is running');
  console.log(`   Version: ${statusResponse.data.version}`);
  console.log(`   Job Queue: ${statusResponse.data.jobQueue.enabled ? 'Enabled' : 'Disabled'}\n`);

  // Test 2: Test the new preview quality settings
  console.log('🎬 Step 2: Testing high-quality preview generation...');
  
  // Simulate uploaded images (using our test session from curl)
  const testImages = [
    { filename: '1752215100751_red.png' },
    { filename: '1752215100752_green.png' }
  ];

  const previewPayload = {
    images: testImages,
    transitions: [{ type: 'fade', duration: 500 }],
    frameDurations: [2000, 2000],
    sessionId: 'test_curl_1752215100' // Using existing session
  };

  const previewResponse = await makeRequest('POST', '/preview', previewPayload);
  
  if (!previewResponse.ok) {
    console.log('❌ Preview generation failed');
    console.log(`   Error: ${previewResponse.data.error}`);
    console.log('   Note: Make sure test images exist from previous curl test');
    return;
  }

  console.log('✅ High-quality preview generated successfully!');
  console.log(`   Filename: ${previewResponse.data.filename}`);
  console.log(`   Quality: 1080p, CRF 18, 5M bitrate, medium preset, high profile`);
  console.log(`   Download: ${API_BASE_URL}${previewResponse.data.previewUrl}\n`);

  const previewFilename = previewResponse.data.filename;

  // Test 3: Test all export formats using the new system
  console.log('🔄 Step 3: Testing format conversions from preview...');
  
  const exportTests = [
    { format: 'mp4', quality: 'high', description: 'MP4 High Quality' },
    { format: 'webm', quality: 'standard', description: 'WebM Standard' },
    { format: 'mov', quality: 'ultra', description: 'MOV Ultra Quality' },
    { format: 'gif', quality: 'standard', description: 'GIF Standard' }
  ];

  const results = [];

  for (const test of exportTests) {
    console.log(`\n   🎯 Testing ${test.description}...`);
    
    const exportPayload = {
      previewFilename: previewFilename,
      format: test.format,
      quality: test.quality,
      sessionId: 'test_curl_1752215100'
    };

    const startTime = Date.now();
    const exportResponse = await makeRequest('POST', '/export/from-preview', exportPayload);
    const duration = Date.now() - startTime;

    if (exportResponse.ok) {
      console.log(`      ✅ ${test.format.toUpperCase()} export completed in ${duration}ms`);
      console.log(`      📁 File: ${exportResponse.data.filename}`);
      console.log(`      🔗 URL: ${API_BASE_URL}${exportResponse.data.downloadUrl}`);
      
      results.push({
        ...test,
        success: true,
        duration,
        filename: exportResponse.data.filename,
        downloadUrl: exportResponse.data.downloadUrl
      });
    } else {
      console.log(`      ❌ ${test.format.toUpperCase()} export failed`);
      console.log(`      Error: ${exportResponse.data.error}`);
      
      results.push({
        ...test,
        success: false,
        error: exportResponse.data.error
      });
    }
  }

  // Test 4: Performance comparison
  console.log('\n📊 Step 4: Performance Analysis');
  console.log('================================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful exports: ${successful.length}/${results.length}`);
  
  if (successful.length > 0) {
    console.log('\n🚀 Export Performance:');
    successful.forEach(r => {
      console.log(`   ${r.format.toUpperCase()} (${r.quality}): ${r.duration}ms`);
    });
    
    const avgTime = successful.reduce((sum, r) => sum + r.duration, 0) / successful.length;
    console.log(`   Average: ${Math.round(avgTime)}ms`);
  }
  
  if (failed.length > 0) {
    console.log('\n❌ Failed exports:');
    failed.forEach(r => {
      console.log(`   ${r.format.toUpperCase()}: ${r.error}`);
    });
  }

  // Test 5: Frontend integration summary
  console.log('\n🎯 Step 5: Frontend Integration Summary');
  console.log('=======================================');
  
  console.log('✅ New System Benefits:');
  console.log('   • Two-phase export process (Preview → Convert)');
  console.log('   • High-quality preview (1080p, CRF 18, 5M bitrate)');
  console.log('   • Consistent quality across all formats');
  console.log('   • Fast format conversion (no image reprocessing)');
  console.log('   • Enhanced progress tracking with detailed steps');
  console.log('   • Auto-download functionality');
  
  console.log('\n🔧 Frontend Components Updated:');
  console.log('   • useExportManagement.ts - New two-phase export logic');
  console.log('   • ExportProgressModal.tsx - Enhanced step tracking');
  console.log('   • ExportControls.tsx - Updated progress display');
  console.log('   • ExportState interface - Added filename support');
  
  console.log('\n🌐 User Experience:');
  console.log('   • Preview shows exactly what will be exported');
  console.log('   • Clear progress indication for both phases');
  console.log('   • Automatic download when export completes');
  console.log('   • Better error handling and feedback');
  
  console.log('\n📱 Next Steps:');
  console.log('   1. Open http://localhost:5181/slideshow in browser');
  console.log('   2. Upload 2 images');
  console.log('   3. Add them to timeline');
  console.log('   4. Generate preview (will be high-quality)');
  console.log('   5. Export to any format (will use preview as base)');
  console.log('   6. Observe two-phase progress and auto-download');

  console.log('\n✅ Frontend integration test completed successfully!');
}

// Run the test
if (require.main === module) {
  testFrontendIntegration().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testFrontendIntegration };
