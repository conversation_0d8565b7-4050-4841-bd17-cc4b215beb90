import React, { useState, useCallback, useMemo } from 'react';
import { MediaItem as MediaItemType, MediaItemConfig, MediaEventHandlers } from '../../types/media.types';
import { defaultMediaTheme, mediaSizes } from '../../theme/mediaTheme';
import MediaThumbnail from './MediaThumbnail';

interface MediaItemProps {
  item: MediaItemType;
  config?: MediaItemConfig;
  handlers?: MediaEventHandlers;
  selected?: boolean;
  dragging?: boolean;
  draggedOver?: boolean;
  className?: string;
  style?: React.CSSProperties;
}

export const MediaItem: React.FC<MediaItemProps> = ({
  item,
  config = {},
  handlers = {},
  selected = false,
  dragging = false,
  draggedOver = false,
  className = '',
  style = {},
}) => {
  const [isHovered, setIsHovered] = useState(false);
  
  const {
    showActions = true,
    showMetadata = true,
    showSelection = false,
    size = 'medium',
    layout = 'list',
    interactive = true,
  } = config;

  const {
    onSelect,
    onDeselect,
    onRemove,
    onAdd,
    onPreview,
    onEdit,
  } = handlers;

  const theme = defaultMediaTheme;
  const sizeConfig = mediaSizes[size];

  const handleClick = useCallback(() => {
    if (!interactive) return;
    
    if (selected && onDeselect) {
      onDeselect(item);
    } else if (!selected && onSelect) {
      onSelect(item);
    }
  }, [item, selected, onSelect, onDeselect, interactive]);

  const handleAddClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onAdd?.(item);
  }, [item, onAdd]);

  const handleRemoveClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onRemove?.(item);
  }, [item, onRemove]);

  const handlePreviewClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onPreview?.(item);
  }, [item, onPreview]);

  const handleEditClick = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onEdit?.(item);
  }, [item, onEdit]);

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const formatDuration = (duration: number): string => {
    const minutes = Math.floor(duration / 60);
    const seconds = Math.floor(duration % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const containerStyle: React.CSSProperties = useMemo(() => ({
    display: 'flex',
    flexDirection: layout === 'list' ? 'row' : 'column',
    alignItems: layout === 'list' ? 'center' : 'stretch',
    gap: theme.spacing.md,
    padding: sizeConfig.padding,
    backgroundColor: selected ? `${theme.colors.primary}15` : theme.colors.surface,
    border: `1px solid ${
      draggedOver ? theme.colors.accent :
      selected ? theme.colors.primary :
      theme.colors.border
    }`,
    borderRadius: theme.borderRadius.md,
    cursor: interactive ? 'pointer' : 'default',
    transition: theme.transitions.normal,
    opacity: dragging ? 0.8 : 1,
    transform: dragging ? 'scale(1.05)' : isHovered && interactive ? 'scale(1.02)' : 'scale(1)',
    boxShadow: dragging ? theme.shadows.lg : 
               isHovered && interactive ? theme.shadows.md : 
               theme.shadows.sm,
    position: 'relative',
    ...style,
  }), [
    layout, selected, draggedOver, dragging, isHovered, interactive,
    theme, sizeConfig, style
  ]);

  const contentStyle: React.CSSProperties = {
    flex: 1,
    minWidth: 0,
    display: 'flex',
    flexDirection: 'column',
    gap: theme.spacing.xs,
  };

  const nameStyle: React.CSSProperties = {
    fontSize: size === 'small' ? '0.75rem' : '0.875rem',
    fontWeight: 'bold',
    color: theme.colors.text,
    margin: 0,
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    fontFamily: '"Space Mono", monospace',
  };

  const metadataStyle: React.CSSProperties = {
    fontSize: '0.75rem',
    color: theme.colors.textSecondary,
    margin: 0,
    fontFamily: '"Space Mono", monospace',
  };

  const actionsStyle: React.CSSProperties = {
    display: 'flex',
    gap: theme.spacing.xs,
    opacity: isHovered || selected ? 1 : 0,
    transition: theme.transitions.fast,
    marginTop: theme.spacing.xs,
  };

  const buttonStyle: React.CSSProperties = {
    padding: `${theme.spacing.xs} ${theme.spacing.sm}`,
    borderRadius: theme.borderRadius.sm,
    border: 'none',
    cursor: 'pointer',
    fontSize: '0.75rem',
    fontWeight: 'bold',
    transition: theme.transitions.fast,
    fontFamily: '"Space Mono", monospace',
  };

  const primaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: theme.colors.primary,
    color: 'white',
  };

  const secondaryButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    backgroundColor: 'transparent',
    color: theme.colors.textSecondary,
    border: `1px solid ${theme.colors.border}`,
  };

  const selectionIndicatorStyle: React.CSSProperties = {
    position: 'absolute',
    top: theme.spacing.xs,
    right: theme.spacing.xs,
    width: '20px',
    height: '20px',
    borderRadius: '50%',
    backgroundColor: selected ? theme.colors.primary : 'transparent',
    border: `2px solid ${selected ? theme.colors.primary : theme.colors.border}`,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    transition: theme.transitions.fast,
  };

  return (
    <div
      className={`media-item ${className}`}
      style={containerStyle}
      onClick={handleClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      title={item.name}
    >
      {/* Selection Indicator */}
      {showSelection && (
        <div style={selectionIndicatorStyle}>
          {selected && (
            <svg width="12" height="12" viewBox="0 0 24 24" fill="white">
              <polyline points="20,6 9,17 4,12" stroke="white" strokeWidth="2" fill="none" />
            </svg>
          )}
        </div>
      )}

      {/* Thumbnail */}
      <MediaThumbnail
        item={item}
        size={size}
        showDuration={item.type === 'video'}
        showType={true}
        onClick={onPreview}
      />

      {/* Content */}
      <div style={contentStyle}>
        {/* Name */}
        <h4 style={nameStyle}>{item.name}</h4>

        {/* Metadata */}
        {showMetadata && (
          <div style={metadataStyle}>
            <div>{formatFileSize(item.size)}</div>
            {item.type === 'video' && (
              <div>{formatDuration((item as any).duration || 0)}</div>
            )}
            {item.type === 'image' && (item as any).dimensions && (
              <div>
                {(item as any).dimensions.width} × {(item as any).dimensions.height}
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        {showActions && interactive && (
          <div style={actionsStyle}>
            {onAdd && (
              <button
                style={primaryButtonStyle}
                onClick={handleAddClick}
                title="Add to timeline"
              >
                Add
              </button>
            )}
            {onPreview && (
              <button
                style={secondaryButtonStyle}
                onClick={handlePreviewClick}
                title="Preview"
              >
                Preview
              </button>
            )}
            {onEdit && (
              <button
                style={secondaryButtonStyle}
                onClick={handleEditClick}
                title="Edit"
              >
                Edit
              </button>
            )}
            {onRemove && (
              <button
                style={{
                  ...secondaryButtonStyle,
                  color: theme.colors.error,
                  borderColor: theme.colors.error,
                }}
                onClick={handleRemoveClick}
                title="Remove"
              >
                Remove
              </button>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default MediaItem;
