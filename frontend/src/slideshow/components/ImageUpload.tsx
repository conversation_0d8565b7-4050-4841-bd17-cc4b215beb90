import React, { useRef } from 'react';
import { useSlideshowContext } from '../context/SlideshowContext';

const ImageUpload: React.FC = () => {
    const {
        project,
        isUploading,
        dragActive,
        uploadImages,
        addToTimeline,
        removeImage,
        setDragActive
    } = useSlideshowContext();

    const fileInputRef = useRef<HTMLInputElement>(null);

    const handleDrag = (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();

        if (e.type === 'dragenter' || e.type === 'dragover') {
            setDragActive(true);
        } else if (e.type === 'dragleave') {
            setDragActive(false);
        }
    };

    const handleDrop = async (e: React.DragEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setDragActive(false);

        if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
            const files = Array.from(e.dataTransfer.files).filter(file =>
                file.type.startsWith('image/')
            );

            if (files.length > 0) {
                try {
                    await uploadImages(files);
                } catch (error) {
                    console.error('Upload failed:', error);
                }
            }
        }
    };

    const handleFileInput = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (e.target.files && e.target.files.length > 0) {
            const files = Array.from(e.target.files);

            try {
                await uploadImages(files);
            } catch (error) {
                console.error('Upload failed:', error);
            }

            // Reset input
            e.target.value = '';
        }
    };

    return (
        <div className="h-full bg-dark-950 flex flex-col p-3">
            <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileInput}
                className="hidden"
            />

            {/* File List */}
            <div className="flex-1 flex flex-col min-h-0">
                {/* Add Image Card - Always visible at top */}
                <div
                    className={`drop-zone h-15 cursor-pointer flex items-center justify-center mb-2 flex-shrink-0 ${dragActive ? 'active' : ''
                        }`}
                    onDragEnter={handleDrag}
                    onDragLeave={handleDrag}
                    onDragOver={handleDrag}
                    onDrop={handleDrop}
                    onClick={() => fileInputRef.current?.click()}
                >
                    <span className={`text-sm font-mono ${dragActive ? 'text-accent-pink' : 'text-dark-400'
                        }`}>
                        {isUploading ? 'Uploading...' : 'Add Images'}
                    </span>
                </div>

                {/* Uploaded Files */}
                <div className="flex-1 overflow-y-auto flex flex-col gap-2 custom-scrollbar">
                    {project.images.map((image) => (
                        <div
                            key={image.id}
                            className="relative w-full"
                        >
                            {/* Full-width Image Container */}
                            <div className="relative w-full h-20 bg-dark-900 border border-dark-700 rounded-lg overflow-hidden shadow-lg transition-all duration-200">
                                <img
                                    src={image.preview}
                                    alt="Media"
                                    className="w-full h-full object-cover"
                                />

                                {/* Main Click Area - Add to Timeline */}
                                <div
                                    className="absolute inset-0 cursor-pointer group/add"
                                    onClick={() => addToTimeline(image.id)}
                                    title="Click to add to timeline"
                                    style={{
                                        clipPath: 'polygon(0 0, calc(100% - 32px) 0, calc(100% - 32px) calc(100% - 32px), 0 calc(100% - 32px))'
                                    }}
                                >
                                    {/* Add Overlay - Only shows when hovering main area */}
                                    <div className="absolute inset-0 bg-accent-pink/5 flex items-center justify-center opacity-0 group-hover/add:opacity-100 transition-opacity duration-200 border border-transparent group-hover/add:border-accent-pink rounded-lg">
                                        <div className="w-5 h-5 bg-accent-pink rounded-full flex items-center justify-center text-white text-sm font-bold shadow-md">
                                            +
                                        </div>
                                    </div>
                                </div>

                                {/* Remove Button Zone - Bottom-right corner */}
                                <button
                                    className="absolute bottom-0 right-0 w-8 h-8 group/remove cursor-pointer flex items-center justify-center z-10 bg-transparent border-none p-0"
                                    onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        removeImage(image.id);
                                    }}
                                    title="Remove image"
                                    type="button"
                                >
                                    {/* Remove Button - Only shows when hovering corner */}
                                    <div className="w-5 h-5 bg-accent-red/90 hover:bg-accent-red text-white text-xs rounded-full flex items-center justify-center transition-all duration-200 opacity-0 group-hover/remove:opacity-100 shadow-md">
                                        <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                    </div>
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Quick Actions */}
                {project.images.length > 0 && (
                    <div className="mt-2 flex gap-2 flex-shrink-0">
                            <button
                                onClick={() => project.images.forEach(img => addToTimeline(img.id))}
                                className="btn-pink flex-1 text-xs py-2"
                            >
                                ALL TO TIMELINE
                            </button>
                        </div>
                )}
            </div>
        </div>
    );
};

export default ImageUpload;
