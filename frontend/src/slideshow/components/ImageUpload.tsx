import React, { useMemo } from 'react';
import { useSlideshowContext } from '../context/SlideshowContext';
import {
  MediaList,
  DropZone,
  MediaItem as MediaItemType,
  UploadConfig,
  MediaEventHandlers,
  MediaListConfig,
  slideshowTheme
} from '../../shared/components/Media';
import { useMediaUpload, useMediaActions } from '../../shared/hooks';

const ImageUpload: React.FC = () => {
    const {
        project,
        uploadImages,
        addToTimeline,
        removeImage,
    } = useSlideshowContext();

    // Convert slideshow images to MediaItem format
    const mediaItems: MediaItemType[] = useMemo(() => {
        return project.images.map(image => ({
            id: image.id,
            file: image.file,
            name: image.name,
            type: 'image' as const,
            size: image.file.size,
            preview: image.preview,
            uploadedInfo: image.uploadedInfo,
            createdAt: new Date(),
            updatedAt: new Date(),
        }));
    }, [project.images]);

    // Upload configuration
    const uploadConfig: UploadConfig = {
        accept: ['image/*'],
        multiple: true,
        maxSize: 10 * 1024 * 1024, // 10MB
        autoUpload: false, // We handle upload manually
    };

    // Media upload hook
    const { uploadFiles, isUploading } = useMediaUpload({
        config: uploadConfig,
        onSuccess: (items) => {
            // Convert MediaItems back to slideshow format and upload
            const files = items.map(item => item.file);
            uploadImages(files);
        },
        onError: (errors) => {
            console.error('Upload validation failed:', errors);
        },
    });

    // Media list configuration
    const listConfig: MediaListConfig = {
        layout: 'list',
        size: 'medium',
        showActions: true,
        showMetadata: false,
        showSelection: false,
        sortable: false,
        selectable: false,
    };

    // Event handlers
    const handlers: MediaEventHandlers = {
        onAdd: (item) => {
            addToTimeline(item.id);
        },
        onRemove: (item) => {
            removeImage(item.id);
        },
        onUpload: async (files) => {
            await uploadFiles(files);
        },
    };

    // Media actions hook
    const { addItems } = useMediaActions(mediaItems, {
        onAdd: (items) => {
            items.forEach(item => addToTimeline(item.id));
        },
        onRemove: (ids) => {
            ids.forEach(id => removeImage(id));
        },
    });

    return (
        <div className="h-full bg-dark-950 flex flex-col p-3">
            {/* Drop Zone */}
            <div className="flex-shrink-0 mb-3">
                <DropZone
                    config={uploadConfig}
                    handlers={handlers}
                    loading={isUploading}
                    className="h-15"
                    style={{
                        minHeight: '60px',
                        backgroundColor: 'transparent',
                        borderColor: '#343536',
                    }}
                >
                    <span className="text-sm font-mono text-dark-400">
                        {isUploading ? 'Uploading...' : 'Add Images'}
                    </span>
                </DropZone>
            </div>

            {/* Media List */}
            <div className="flex-1 min-h-0">
                <MediaList
                    items={mediaItems}
                    config={listConfig}
                    handlers={handlers}
                    loading={isUploading}
                    theme={slideshowTheme}
                    className="h-full"
                    style={{
                        backgroundColor: 'transparent',
                        padding: 0,
                    }}
                />
            </div>

            {/* Quick Actions */}
            {mediaItems.length > 0 && (
                <div className="mt-3 flex gap-2 flex-shrink-0">
                    <button
                        onClick={() => {
                            mediaItems.forEach(item => addToTimeline(item.id));
                        }}
                        className="btn-pink flex-1 text-xs py-2"
                        disabled={isUploading}
                    >
                        ALL TO TIMELINE
                    </button>
                </div>
            )}
        </div>
    );
};

export default ImageUpload;
