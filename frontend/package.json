{"name": "animagen-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@types/react-transition-group": "^4.4.12", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hotkeys-hook": "^5.1.0", "react-router-dom": "^7.6.3", "react-transition-group": "^4.4.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.19", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "vite": "^6.3.5"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "*"}}