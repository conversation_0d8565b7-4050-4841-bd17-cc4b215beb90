#!/usr/bin/env node

/**
 * Test script para probar el nuevo flujo Preview -> Export
 * Prueba la calidad mejorada del preview y la conversión a diferentes formatos
 */

const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

// Helper function to make HTTP requests
async function makeRequest(method, endpoint, data = null) {
  const url = `${API_BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return {
      status: response.status,
      data: result,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      data: { error: error.message },
      ok: false
    };
  }
}

async function testPreviewExportFlow() {
  console.log('🧪 Testing Preview -> Export Flow');
  console.log('=====================================\n');

  const sessionId = `test_preview_export_${Date.now()}`;
  
  // Test data - simulating 2 uploaded images
  const testImages = [
    { filename: 'test_image_1.png' },
    { filename: 'test_image_2.png' }
  ];

  const testTransitions = [
    { type: 'fade', duration: 500 }
  ];

  const testFrameDurations = [2000, 2000]; // 2 seconds each

  console.log('📋 Test Configuration:');
  console.log(`   Session ID: ${sessionId}`);
  console.log(`   Images: ${testImages.length}`);
  console.log(`   Frame durations: ${testFrameDurations.join(', ')}ms`);
  console.log(`   Transitions: ${testTransitions.map(t => `${t.type} (${t.duration}ms)`).join(', ')}\n`);

  // Step 1: Test high-quality preview generation
  console.log('🎬 Step 1: Generating high-quality preview...');
  const previewData = {
    images: testImages,
    transitions: testTransitions,
    frameDurations: testFrameDurations,
    sessionId: sessionId
  };

  const previewResponse = await makeRequest('POST', '/preview', previewData);
  console.log(`   Status: ${previewResponse.status}`);
  
  if (!previewResponse.ok) {
    console.log('❌ Preview generation failed:');
    console.log(`   Error: ${JSON.stringify(previewResponse.data, null, 2)}`);
    return;
  }

  console.log('✅ High-quality preview generated!');
  console.log(`   Preview URL: ${previewResponse.data.previewUrl}`);
  console.log(`   Preview filename: ${previewResponse.data.filename}`);
  
  const previewFilename = previewResponse.data.filename;

  // Step 2: Test conversion to different formats
  const formats = ['mp4', 'webm', 'mov', 'gif'];
  const qualities = ['standard', 'high'];

  for (const format of formats) {
    for (const quality of qualities) {
      console.log(`\n🔄 Step 2.${formats.indexOf(format) + 1}.${qualities.indexOf(quality) + 1}: Converting to ${format.toUpperCase()} (${quality})...`);
      
      const exportData = {
        previewFilename: previewFilename,
        format: format,
        quality: quality,
        sessionId: sessionId
      };

      const exportResponse = await makeRequest('POST', '/export/from-preview', exportData);
      console.log(`   Status: ${exportResponse.status}`);
      
      if (exportResponse.ok) {
        console.log(`✅ ${format.toUpperCase()} export completed!`);
        console.log(`   Download URL: ${exportResponse.data.downloadUrl}`);
        console.log(`   Filename: ${exportResponse.data.filename}`);
        console.log(`   Quality: ${exportResponse.data.quality}`);
      } else {
        console.log(`❌ ${format.toUpperCase()} export failed:`);
        console.log(`   Error: ${JSON.stringify(exportResponse.data, null, 2)}`);
      }
    }
  }

  // Step 3: Test quality comparison
  console.log('\n📊 Step 3: Quality Analysis');
  console.log('============================');
  console.log('Preview Settings (New):');
  console.log('   - Resolution: Up to 1920x1080 (Full HD)');
  console.log('   - CRF: 18 (High quality)');
  console.log('   - Bitrate: 5M (High bitrate)');
  console.log('   - Preset: medium (Quality over speed)');
  console.log('   - Profile: high (Better compression)');
  console.log('');
  console.log('Previous Preview Settings:');
  console.log('   - Resolution: Up to 1280x720 (HD Ready)');
  console.log('   - CRF: 23 (Medium quality)');
  console.log('   - Bitrate: 2M (Standard bitrate)');
  console.log('   - Preset: ultrafast (Speed over quality)');
  console.log('   - Profile: baseline (Basic compatibility)');

  console.log('\n🎯 Benefits of New Flow:');
  console.log('   ✅ Single high-quality source for all formats');
  console.log('   ✅ Consistent quality between preview and export');
  console.log('   ✅ Faster export (no re-processing from images)');
  console.log('   ✅ Better compression efficiency');
  console.log('   ✅ Eliminates preview vs export discrepancies');

  console.log('\n✅ Preview -> Export flow test completed!');
}

// Run the test
if (require.main === module) {
  testPreviewExportFlow().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testPreviewExportFlow };
