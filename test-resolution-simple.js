const fs = require('fs');
const path = require('path');
const FormData = require('form-data');
const fetch = require('node-fetch');

const API_BASE = 'http://localhost:3001';

async function testResolutionSimple() {
  const sessionId = `test_resolution_${Date.now()}`;
  
  try {
    // 1. Upload images
    console.log('1. Uploading test images...');
    const form = new FormData();
    const testImages = ['image1.jpg', 'image2.jpg'];
    
    for (const img of testImages) {
      const imagePath = path.join(__dirname, 'test_images', img);
      if (fs.existsSync(imagePath)) {
        form.append('images', fs.createReadStream(imagePath), img);
      }
    }
    
    const uploadRes = await fetch(`${API_BASE}/upload?sessionId=${sessionId}`, {
      method: 'POST',
      body: form
    });
    
    const uploadData = await uploadRes.json();
    console.log('✅ Upload result:', uploadData);
    
    // 2. Test custom resolution export
    console.log('\n2. Testing custom resolution (1000x1000)...');
    const videoPayload = {
      sessionId,
      images: uploadData.files.map(f => ({ filename: f.filename })),
      frameDurations: [3000, 3000],
      format: 'mp4',
      fps: 30,
      quality: 'high',
      resolution: 'custom',
      videoConfig: {
        resolution: {
          width: 1000,
          height: 1000
        }
      }
    };
    
    console.log('📤 Sending payload:', JSON.stringify(videoPayload, null, 2));
    
    const videoRes = await fetch(`${API_BASE}/video-simple`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(videoPayload)
    });
    
    const videoData = await videoRes.json();
    console.log('✅ Video result:', videoData);
    
    if (videoData.success) {
      console.log('\n✅ CUSTOM RESOLUTION WORKS!');
      console.log('📁 Video file:', videoData.filename);
    } else {
      console.log('\n❌ CUSTOM RESOLUTION FAILED:', videoData.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testResolutionSimple();
