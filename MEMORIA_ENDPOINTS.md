# 📝 MEMORIA: Sistema de Endpoints de AnimaGen

## ✅ ENDPOINTS UTILIZADOS ACTUALMENTE:

### 🎯 **Frontend usa endpoints simples para exportación directa:**
- `/gif-simple` - Para exportar GIFs
- `/video-simple` - Para exportar MP4/WebM/MOV

### 🔧 **Ubicación del código:**
- **Frontend**: `frontend/src/slideshow/hooks/useExportManagement.ts` - función `exportAPI`
- **Backend**: `backend/routes/export.js` - rutas `/gif-simple` y `/video-simple`

### 🔄 **Endpoints de soporte:**
- `/api/unified-export/:format` - Endpoint unificado (usado como fallback)
- `/status/:jobId` - Consultar estado de trabajos en cola
- `/download/:jobId` - Descargar resultado de trabajos en cola
- `/debug/:jobId` - Información de depuración para trabajos

## ❌ ENDPOINTS LEGACY (ELIMINADOS):
- `/export/mp4` - ❌ Eliminado (reemplazado por `/video-simple`)
- `/export/webm` - ❌ Eliminado (reemplazado por `/video-simple`)
- `/export/mov` - ❌ Eliminado (reemplazado por `/video-simple`)
- `/export/gif` - ❌ Eliminado (reemplazado por `/gif-simple`)
- `/export/video` - ❌ Eliminado (reemplazado por `/video-simple`)

## 📊 FLUJO DE EXPORTACIÓN:
1. Frontend llama a `/gif-simple` o `/video-simple`
2. Backend procesa directamente y devuelve URL de descarga
3. Frontend descarga el archivo resultante

## 🔮 PLAN FUTURO:
- Migrar gradualmente al sistema unificado basado en cola
- Implementar feature flag para controlar el uso de colas vs. procesamiento directo
- Eventualmente usar exclusivamente `/api/unified-export/:format` para todas las exportaciones
