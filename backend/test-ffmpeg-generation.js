#!/usr/bin/env node

/**
 * Test script to verify FFmpeg command generation with actual test images
 */

const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const execAsync = promisify(require('child_process').exec);

// Import the transition effects mapping from unified-export
const transitionEffects = {
  none: 'none',
  cut: 'none',
  fade: 'fade',
  fadeblack: 'fadeblack',
  fadewhite: 'fadewhite',
  dissolve: 'dissolve',
  slideleft: 'slideleft',
  slideright: 'slideright',
  slideup: 'slideup',
  slidedown: 'slidedown',
  wipeleft: 'wipeleft',
  wiperight: 'wiperight',
  wipeup: 'wipeup',
  wipedown: 'wipedown',
  wipetl: 'wipetl',
  wipetr: 'wipetr',
  circleopen: 'circleopen',
  circleclose: 'circleclose',
  zoomin: 'zoomin',
  slide: 'slideleft', // Legacy
  zoom: 'zoomin'      // Legacy
};

// Build unified transition chain function (simplified version)
function buildUnifiedTransitionChain(validImages, transitions, frameDurations, duration, complexFilter) {
  console.log(`buildUnifiedTransitionChain: ${validImages.length} images, ${transitions?.length || 0} transitions`);
  if (validImages.length === 1) {
    console.log('Single image, returning [v0]');
    return '[v0]';
  }
  
  // Check if all transitions are missing or are cut/none types
  const hasAnyRealTransitions = transitions && transitions.some(t => 
    t && t.type && t.type !== 'cut' && t.type !== 'none' && (t.duration || 0) > 0
  );
  
  if (!transitions || validImages.length < 2 || !hasAnyRealTransitions) {
    // No transitions or all are cut/none - use simple concat
    console.log('No real transitions detected, using concat');
    let concatVideo = "";
    for(let i = 0; i < validImages.length; i++){
      concatVideo += `[v${i}]`;
    }
    complexFilter.push(`${concatVideo}concat=n=${validImages.length}[outv]`);
    console.log(`Concat filter: ${concatVideo}concat=n=${validImages.length}[outv]`);
    return '[outv]';
  }
  
  // Build transition chain with xfade
  console.log('Using xfade transition chain');
  let lastOutput = '[v0]';
  let totalVideoTime = 0;
  
  for (let i = 0; i < validImages.length - 1; i++) {
    const currentFrameDuration = (frameDurations[i] || duration) / 1000;
    const transition = transitions[i] || { type: 'fade', duration: 500 };
    
    // transition.duration comes in milliseconds from frontend, convert to seconds for FFmpeg
    let transitionDuration = (transition.type && !['none', 'cut'].includes(transition.type))
      ? Math.max(transition.duration / 1000, 0.1)
      : 0.001;
    let transitionType = transitionEffects[transition.type] || 'fade';
    
    // Always use a real effect, even for cuts (with minimal duration)
    if (['none', 'cut'].includes(transition.type)) {
      transitionType = 'fade';
    }
    
    const nextInput = `[v${i + 1}]`;
    const outputLabel = (i === validImages.length - 2) ? '[outv]' : `[t${i}]`;
    
    // Offset should be at the END of the current frame, not beginning + duration
    const offset = totalVideoTime + currentFrameDuration - transitionDuration;
    totalVideoTime += currentFrameDuration;
    
    const xfadeFilter = `${lastOutput}${nextInput}xfade=transition=${transitionType}:duration=${transitionDuration}:offset=${offset}${outputLabel}`;
    console.log(`Frame ${i}->${i+1}: duration=${currentFrameDuration}s, offset=${offset}s, transition=${transitionDuration}s`);
    console.log(`XFade filter: ${xfadeFilter}`);
    complexFilter.push(xfadeFilter);
    lastOutput = outputLabel;
  }
  
  console.log(`Total xfade transitions processed: ${validImages.length - 1}, final output: ${lastOutput}`);
  return lastOutput;
}

// Test FFmpeg command generation
async function testFFmpegCommandGeneration() {
  console.log('🧪 Testing FFmpeg Command Generation with Test Images\n');
  
  // Check if test images exist
  const testImagesDir = path.join(__dirname, '..', 'test-images');
  const testImages = ['test1.jpg', 'test2.jpg', 'test3.jpg'];
  
  console.log(`Looking for test images in: ${testImagesDir}`);
  
  const availableImages = [];
  for (const imageName of testImages) {
    const imagePath = path.join(testImagesDir, imageName);
    if (fs.existsSync(imagePath)) {
      availableImages.push({ filename: imageName, path: imagePath });
      console.log(`✅ Found: ${imageName}`);
    } else {
      console.log(`❌ Missing: ${imageName}`);
    }
  }
  
  if (availableImages.length < 2) {
    console.log('❌ Need at least 2 test images to test transitions');
    return;
  }
  
  // Test scenarios
  const testScenarios = [
    {
      name: 'Two images with fade transition',
      images: availableImages.slice(0, 2),
      transitions: [{ type: 'fade', duration: 500 }],
      frameDurations: [1000, 1000],
      format: 'mp4'
    },
    {
      name: 'Three images with multiple transitions',
      images: availableImages.slice(0, 3),
      transitions: [{ type: 'slideleft', duration: 600 }, { type: 'wipeup', duration: 700 }],
      frameDurations: [1000, 1500, 2000],
      format: 'mp4'
    },
    {
      name: 'Two images GIF with transition',
      images: availableImages.slice(0, 2),
      transitions: [{ type: 'circleopen', duration: 800 }],
      frameDurations: [1000, 1000],
      format: 'gif'
    }
  ];
  
  for (const scenario of testScenarios) {
    console.log(`\n--- Testing: ${scenario.name} ---`);
    
    try {
      // Generate input flags
      const inputFlags = [];
      scenario.images.forEach((img, index) => {
        const duration = (scenario.frameDurations[index] || 1000) / 1000;
        inputFlags.push(`-loop 1 -t ${duration} -i "${img.path}"`);
      });
      
      // Generate scale filters
      const targetResolution = { width: 720, height: 720 };
      const scaleFilters = scenario.images.map((_, index) =>
        `[${index}:v]scale=${targetResolution.width}:${targetResolution.height}:force_original_aspect_ratio=decrease,pad=${targetResolution.width}:${targetResolution.height}:(ow-iw)/2:(oh-ih)/2[v${index}]`
      );
      
      // Build transition chain
      let complexFilterArray = [...scaleFilters];
      const lastOutput = buildUnifiedTransitionChain(
        scenario.images,
        scenario.transitions,
        scenario.frameDurations,
        1000,
        complexFilterArray
      );
      
      // Handle output mapping
      let finalOutput = lastOutput;
      if (scenario.format === 'gif') {
        if (lastOutput === '[outv]') {
          const lastFilterIndex = complexFilterArray.length - 1;
          if (lastFilterIndex >= 0 && complexFilterArray[lastFilterIndex].includes('[outv]')) {
            complexFilterArray[lastFilterIndex] = complexFilterArray[lastFilterIndex].replace('[outv]', '[out]');
            finalOutput = '[out]';
          } else {
            complexFilterArray.push(`${lastOutput}null[out]`);
            finalOutput = '[out]';
          }
        }
        
        // Add GIF palette generation
        complexFilterArray.push(`${finalOutput}split[s0][s1]`);
        complexFilterArray.push(`[s0]palettegen[p]`);
        complexFilterArray.push(`[s1][p]paletteuse[outgif]`);
        finalOutput = '[outgif]';
      } else {
        if (lastOutput === '[outv]') {
          const lastFilterIndex = complexFilterArray.length - 1;
          if (lastFilterIndex >= 0 && complexFilterArray[lastFilterIndex].includes('[outv]')) {
            complexFilterArray[lastFilterIndex] = complexFilterArray[lastFilterIndex].replace('[outv]', '[out]');
            finalOutput = '[out]';
          } else {
            complexFilterArray.push(`${lastOutput}null[out]`);
            finalOutput = '[out]';
          }
        }
      }
      
      const filterComplex = complexFilterArray.join(';');
      
      // Generate output options
      let outputOptions;
      if (scenario.format === 'gif') {
        outputOptions = `-map "${finalOutput}" -r 15 -loop 0`;
      } else {
        outputOptions = `-map "${finalOutput}" -c:v libx264 -preset fast -crf 23 -pix_fmt yuv420p -movflags +faststart -r 30`;
      }
      
      // Generate output file
      const outputFile = `/tmp/test_export_${Date.now()}.${scenario.format}`;
      
      // Build final command
      const ffmpegCmd = `ffmpeg ${inputFlags.join(' ')} -filter_complex "${filterComplex}" ${outputOptions} -y "${outputFile}"`;
      
      console.log(`✅ Generated FFmpeg command:`);
      console.log(`   ${ffmpegCmd}`);
      console.log(`✅ Filter complex breakdown:`);
      const filterParts = filterComplex.split(';');
      filterParts.forEach((part, index) => {
        console.log(`   ${index + 1}: ${part}`);
      });
      
      // Test the command (dry run - just validate syntax)
      console.log(`🧪 Testing command execution...`);
      const testResult = await execAsync(ffmpegCmd);
      console.log(`✅ Command executed successfully!`);
      
      // Check if output file was created
      if (fs.existsSync(outputFile)) {
        const stats = fs.statSync(outputFile);
        console.log(`✅ Output file created: ${stats.size} bytes`);
        
        // Clean up
        fs.unlinkSync(outputFile);
      } else {
        console.log(`❌ Output file was not created`);
      }
      
    } catch (error) {
      console.log(`❌ Error in scenario: ${error.message}`);
      if (error.stderr) {
        console.log(`FFmpeg stderr: ${error.stderr}`);
      }
    }
  }
}

// Main execution
async function main() {
  try {
    await testFFmpegCommandGeneration();
  } catch (error) {
    console.error('Test failed:', error);
  }
}

main();
