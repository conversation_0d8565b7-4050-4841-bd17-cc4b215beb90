// Debug server - Progressive loading to identify hanging point
console.log('🔧 Starting debug server...');

// Step 1: Basic imports
console.log('📦 Loading basic modules...');
require('dotenv').config();
const express = require('express');
const cors = require('cors');
console.log('✅ Basic modules loaded');

// Step 2: Create app
console.log('🚀 Creating Express app...');
const app = express();
app.use(cors());
app.use(express.json());
console.log('✅ Express app created');

// Step 3: Basic route
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'Debug server running',
    timestamp: new Date().toISOString()
  });
});
console.log('✅ Basic route added');

// Step 4: Try Redis connection
console.log('🔍 Testing Redis connection...');
try {
  const { testRedisConnection } = require('./utils/redis');
  testRedisConnection()
    .then(() => {
      console.log('✅ Redis connection successful');
      startServer();
    })
    .catch((error) => {
      console.log('❌ Redis connection failed:', error.message);
      console.log('⚠️ Continuing without Redis...');
      startServer();
    });
} catch (error) {
  console.log('❌ Redis module load failed:', error.message);
  startServer();
}

function startServer() {
  console.log('🌐 Starting HTTP server...');
  const PORT = process.env.PORT || 3001;
  
  const server = app.listen(PORT, '0.0.0.0', (err) => {
    if (err) {
      console.error('❌ Failed to start server:', err);
      process.exit(1);
    }
    console.log(`✅ Debug server running on port ${PORT}`);
    console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
  });

  // Graceful shutdown
  process.on('SIGTERM', () => {
    console.log('🛑 Shutting down gracefully...');
    server.close(() => {
      console.log('✅ Server closed');
      process.exit(0);
    });
  });
}