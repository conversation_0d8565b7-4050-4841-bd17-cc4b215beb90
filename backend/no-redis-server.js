// Server without Redis to isolate the problem
console.log('🔧 Starting no-redis server...');

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const { addPlaceholderEndpoints } = require('./placeholder-video');

console.log('✅ Basic modules loaded');

const app = express();

// Enhanced CORS configuration
app.use(cors({
  origin: ["http://localhost:5173", "http://localhost:5174", "http://localhost:5175", "http://localhost:5176", "http://localhost:5177", "http://localhost:5178"],
  methods: ["GET", "POST", "OPTIONS", "PUT", "DELETE"],
  allowedHeaders: ["Content-Type", "Authorization", "X-Requested-With", "Accept", "Origin"],
  credentials: true,
  preflightContinue: false,
  optionsSuccessStatus: 200
}));

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

console.log('✅ Middleware configured');

// Basic multer setup for file uploads
const upload = multer({
  dest: 'temp/',
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
    files: 50
  }
});

console.log('✅ Multer configured');

// Health check endpoint
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: 'No-Redis server running',
    timestamp: new Date().toISOString(),
    redis: 'disabled',
    queue: 'disabled',
    mode: 'stable-mock'
  });
});

// Debug endpoint for frontend
app.get('/api/status', (req, res) => {
  res.json({
    server: 'AnimaGen Backend',
    mode: 'stable-mock',
    features: {
      upload: true,
      preview: 'mock',
      export: 'disabled',
      processing: false
    },
    endpoints: {
      upload: 'http://localhost:3001/upload',
      preview: 'http://localhost:3001/preview',
      placeholder: 'http://localhost:3001/api/placeholder-preview.mp4'
    },
    note: 'Server running in stable mode - no real processing available'
  });
});

// Upload endpoint with detailed logging
app.post('/upload', upload.array('images', 50), (req, res) => {
  console.log('📤 Upload request received');
  console.log('Headers:', req.headers);
  console.log('Method:', req.method);
  console.log('URL:', req.url);
  console.log('Files:', req.files?.length || 0);
  console.log('Session ID:', req.query.sessionId);
  console.log('Body keys:', Object.keys(req.body || {}));
  
  try {
    const response = {
      success: true,
      message: 'Upload received (processing disabled)',
      sessionId: req.query.sessionId,
      fileCount: req.files?.length || 0,
      files: req.files?.map(f => ({
        originalname: f.originalname,
        size: f.size,
        path: f.path
      })) || [],
      timestamp: new Date().toISOString()
    };
    
    console.log('📤 Sending response:', JSON.stringify(response, null, 2));
    res.json(response);
  } catch (error) {
    console.error('❌ Upload error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Preview endpoint (mock)
app.post('/preview', (req, res) => {
  console.log('👁️ Preview request received');
  res.json({
    success: true,
    message: 'Preview generation disabled (stable mode)',
    previewUrl: '/api/placeholder-preview.mp4',
    sessionId: req.body.sessionId,
    note: 'Server running in stable mode - no processing available'
  });
});

// Add placeholder video endpoints
addPlaceholderEndpoints(app);

// Video-simple endpoint (for export API compatibility)
app.post('/video-simple', (req, res) => {
  console.log('🎬 Video-simple export request (mock)');
  console.log('Export format:', req.body.format);
  console.log('Session ID:', req.body.sessionId);
  console.log('Images count:', req.body.images?.length || 0);
  
  res.json({
    success: false,
    message: 'Export disabled in stable mode',
    format: req.body.format || 'mp4',
    sessionId: req.body.sessionId,
    note: 'Server running without processing capabilities - export functionality disabled',
    mockResponse: true
  });
});

// Export endpoints (mock for compatibility)
app.post('/export/gif', (req, res) => {
  console.log('🎬 GIF export request (disabled)');
  res.json({
    success: false,
    message: 'Export disabled in stable mode',
    note: 'Server running without processing capabilities'
  });
});

app.post('/export/mp4', (req, res) => {
  console.log('🎬 MP4 export request (disabled)');
  res.json({
    success: false,
    message: 'Export disabled in stable mode',
    note: 'Server running without processing capabilities'
  });
});

app.post('/export/webm', (req, res) => {
  console.log('🎬 WebM export request (disabled)');
  res.json({
    success: false,
    message: 'Export disabled in stable mode',
    note: 'Server running without processing capabilities'
  });
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    app: 'AnimaGen Backend',
    version: '1.0.0-stable',
    status: 'running',
    mode: 'stable-no-redis',
    endpoints: {
      health: '/api/health',
      upload: '/upload',
      preview: '/preview',
      videoSimple: '/video-simple',
      exports: ['/export/gif', '/export/mp4', '/export/webm'],
      placeholder: '/api/placeholder-preview.mp4'
    },
    features: {
      upload: 'enabled',
      preview: 'mock',
      processing: 'disabled',
      queue: 'disabled',
      redis: 'disabled'
    }
  });
});

const PORT = process.env.PORT || 3001;

console.log('🌐 Starting server...');

const server = app.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ Failed to start server:', err);
    process.exit(1);
  }
  console.log(`✅ No-Redis server running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📤 Upload test: curl -X POST http://localhost:${PORT}/upload`);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Graceful shutdown initiated...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🛑 Ctrl+C received, shutting down...');
  server.close(() => {
    console.log('✅ Server closed');
    process.exit(0);
  });
});