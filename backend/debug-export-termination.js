#!/usr/bin/env node

/**
 * Debug script to test export termination issues
 */

const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const execAsync = promisify(require('child_process').exec);

// Test the buildUnifiedTransitionChain function in isolation
function testBuildUnifiedTransitionChain() {
  console.log('🧪 Testing buildUnifiedTransitionChain function...\n');
  
  // Import the transition effects mapping
  const transitionEffects = {
    fade: 'fade',
    slideleft: 'slideleft',
    wipeup: 'wipeup',
    circleopen: 'circleopen',
    zoomin: 'zoomin'
  };

  // Simplified version of the function for testing
  function buildUnifiedTransitionChain(validImages, transitions, frameDurations, duration, complexFilter) {
    console.log(`buildUnifiedTransitionChain: ${validImages.length} images, ${transitions?.length || 0} transitions`);
    if (validImages.length === 1) {
      console.log('Single image, returning [v0]');
      return '[v0]';
    }
    
    // Check if all transitions are missing or are cut/none types
    const hasAnyRealTransitions = transitions && transitions.some(t => 
      t && t.type && t.type !== 'cut' && t.type !== 'none' && (t.duration || 0) > 0
    );
    
    if (!transitions || validImages.length < 2 || !hasAnyRealTransitions) {
      // No transitions or all are cut/none - use simple concat
      console.log('No real transitions detected, using concat');
      let concatVideo = "";
      for(let i = 0; i < validImages.length; i++){
        concatVideo += `[v${i}]`;
      }
      complexFilter.push(`${concatVideo}concat=n=${validImages.length}[outv]`);
      console.log(`Concat filter: ${concatVideo}concat=n=${validImages.length}[outv]`);
      return '[outv]';
    }
    
    // Build transition chain with xfade
    console.log('Using xfade transition chain');
    let lastOutput = '[v0]';
    let totalVideoTime = 0;
    
    for (let i = 0; i < validImages.length - 1; i++) {
      const currentFrameDuration = (frameDurations[i] || duration) / 1000;
      const transition = transitions[i] || { type: 'fade', duration: 500 };
      
      // transition.duration comes in milliseconds from frontend, convert to seconds for FFmpeg
      let transitionDuration = (transition.type && !['none', 'cut'].includes(transition.type))
        ? Math.max(transition.duration / 1000, 0.1)
        : 0.001;
      let transitionType = transitionEffects[transition.type] || 'fade';
      
      // Always use a real effect, even for cuts (with minimal duration)
      if (['none', 'cut'].includes(transition.type)) {
        transitionType = 'fade';
      }
      
      const nextInput = `[v${i + 1}]`;
      const outputLabel = (i === validImages.length - 2) ? '[outv]' : `[t${i}]`;
      
      // Offset should be at the END of the current frame, not beginning + duration
      const offset = totalVideoTime + currentFrameDuration - transitionDuration;
      totalVideoTime += currentFrameDuration;
      
      const xfadeFilter = `${lastOutput}${nextInput}xfade=transition=${transitionType}:duration=${transitionDuration}:offset=${offset}${outputLabel}`;
      console.log(`Frame ${i}->${i+1}: duration=${currentFrameDuration}s, offset=${offset}s, transition=${transitionDuration}s`);
      console.log(`XFade filter: ${xfadeFilter}`);
      complexFilter.push(xfadeFilter);
      lastOutput = outputLabel;
    }
    
    console.log(`Total xfade transitions processed: ${validImages.length - 1}, final output: ${lastOutput}`);
    return lastOutput;
  }

  // Test scenarios
  const testCases = [
    {
      name: "Single image",
      images: [{ path: 'test1.jpg' }],
      transitions: [],
      frameDurations: [1000]
    },
    {
      name: "Two images with fade",
      images: [{ path: 'test1.jpg' }, { path: 'test2.jpg' }],
      transitions: [{ type: 'fade', duration: 500 }],
      frameDurations: [1000, 1000]
    },
    {
      name: "Three images with transitions",
      images: [{ path: 'test1.jpg' }, { path: 'test2.jpg' }, { path: 'test3.jpg' }],
      transitions: [{ type: 'slideleft', duration: 600 }, { type: 'wipeup', duration: 700 }],
      frameDurations: [1000, 1500, 2000]
    }
  ];

  for (const testCase of testCases) {
    console.log(`\n--- Testing: ${testCase.name} ---`);
    const complexFilter = [];
    
    try {
      const result = buildUnifiedTransitionChain(
        testCase.images,
        testCase.transitions,
        testCase.frameDurations,
        1000,
        complexFilter
      );
      
      console.log(`✅ Result: ${result}`);
      console.log(`✅ Complex filters generated: ${complexFilter.length}`);
      complexFilter.forEach((filter, index) => {
        console.log(`   ${index + 1}: ${filter}`);
      });
    } catch (error) {
      console.log(`❌ Error: ${error.message}`);
      console.log(`❌ Stack: ${error.stack}`);
    }
  }
}

// Test FFmpeg command generation
async function testFFmpegCommand() {
  console.log('\n🧪 Testing FFmpeg command generation...\n');
  
  // Test a simple FFmpeg command to see if FFmpeg is working
  try {
    console.log('Testing FFmpeg availability...');
    const { stdout, stderr } = await execAsync('ffmpeg -version');
    console.log('✅ FFmpeg is available');
    console.log(`Version info: ${stdout.split('\n')[0]}`);
  } catch (error) {
    console.log('❌ FFmpeg is not available or has issues:');
    console.log(`Error: ${error.message}`);
    return;
  }

  // Test a simple filter complex command
  try {
    console.log('\nTesting simple filter complex...');
    const testCmd = 'ffmpeg -f lavfi -i color=red:size=320x240:duration=1 -f lavfi -i color=blue:size=320x240:duration=1 -filter_complex "[0:v][1:v]xfade=transition=fade:duration=0.5:offset=0.5[out]" -map "[out]" -t 1.5 -y /tmp/test_xfade.mp4';
    
    console.log(`Command: ${testCmd}`);
    const { stdout, stderr } = await execAsync(testCmd);
    console.log('✅ Simple xfade command works');
    
    // Clean up test file
    try {
      fs.unlinkSync('/tmp/test_xfade.mp4');
    } catch (e) {
      // Ignore cleanup errors
    }
  } catch (error) {
    console.log('❌ Simple xfade command failed:');
    console.log(`Error: ${error.message}`);
    console.log(`Stderr: ${error.stderr || 'No stderr'}`);
  }
}

// Main execution
async function main() {
  console.log('🔍 Export Termination Debug Script');
  console.log('===================================\n');
  
  testBuildUnifiedTransitionChain();
  await testFFmpegCommand();
  
  console.log('\n🎯 Debug Summary');
  console.log('================');
  console.log('1. Check if buildUnifiedTransitionChain generates valid filters');
  console.log('2. Check if FFmpeg commands are properly formed');
  console.log('3. Check if FFmpeg is available and working');
  console.log('4. Look for any obvious errors in the transition processing logic');
  console.log('\nNext steps:');
  console.log('- Check backend console logs during export');
  console.log('- Test with actual image files');
  console.log('- Monitor memory usage during export');
  console.log('- Check if specific transition types cause issues');
}

main().catch(console.error);
