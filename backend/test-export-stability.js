#!/usr/bin/env node

/**
 * Test script to verify export stability and identify termination issues
 */

const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const execAsync = promisify(require('child_process').exec);

// Test export stability with various scenarios
async function testExportStability() {
  console.log('🧪 Testing Export Stability\n');
  
  // Check if test images exist
  const testImagesDir = path.join(__dirname, '..', 'test-images');
  const testImages = ['test1.jpg', 'test2.jpg', 'test3.jpg', 'test4.jpg'];
  
  console.log(`Looking for test images in: ${testImagesDir}`);
  
  const availableImages = [];
  for (const imageName of testImages) {
    const imagePath = path.join(testImagesDir, imageName);
    if (fs.existsSync(imagePath)) {
      availableImages.push({ filename: imageName, path: imagePath });
      console.log(`✅ Found: ${imageName}`);
    } else {
      console.log(`❌ Missing: ${imageName}`);
    }
  }
  
  if (availableImages.length < 2) {
    console.log('❌ Need at least 2 test images to test exports');
    return;
  }
  
  // Test scenarios that might cause termination
  const testScenarios = [
    {
      name: 'Single image (no transitions)',
      images: availableImages.slice(0, 1),
      transitions: [],
      frameDurations: [2000],
      format: 'mp4',
      expectedBehavior: 'Should work with simple processing'
    },
    {
      name: 'Two images with simple fade',
      images: availableImages.slice(0, 2),
      transitions: [{ type: 'fade', duration: 500 }],
      frameDurations: [1000, 1000],
      format: 'mp4',
      expectedBehavior: 'Should work with basic transition'
    },
    {
      name: 'Three images (direct processing threshold)',
      images: availableImages.slice(0, 3),
      transitions: [{ type: 'slideleft', duration: 600 }, { type: 'wipeup', duration: 700 }],
      frameDurations: [1000, 1500, 2000],
      format: 'mp4',
      expectedBehavior: 'Should use direct processing path'
    },
    {
      name: 'Four images (worker processing)',
      images: availableImages.slice(0, 4),
      transitions: [
        { type: 'fade', duration: 500 },
        { type: 'circleopen', duration: 800 },
        { type: 'zoomin', duration: 600 }
      ],
      frameDurations: [1000, 1500, 2000, 1200],
      format: 'mp4',
      expectedBehavior: 'Should use worker processing path'
    },
    {
      name: 'GIF with transitions',
      images: availableImages.slice(0, 3),
      transitions: [{ type: 'fade', duration: 500 }, { type: 'slideright', duration: 600 }],
      frameDurations: [1000, 1000, 1000],
      format: 'gif',
      expectedBehavior: 'Should create GIF with transitions'
    },
    {
      name: 'Long duration test',
      images: availableImages.slice(0, 2),
      transitions: [{ type: 'fade', duration: 2000 }],
      frameDurations: [5000, 5000],
      format: 'mp4',
      expectedBehavior: 'Should handle longer durations'
    },
    {
      name: 'Complex transitions test',
      images: availableImages.slice(0, 3),
      transitions: [{ type: 'circleopen', duration: 1000 }, { type: 'pixelize', duration: 1200 }],
      frameDurations: [2000, 2500, 3000],
      format: 'mp4',
      expectedBehavior: 'Should handle complex transition effects'
    }
  ];
  
  let passedTests = 0;
  let failedTests = 0;
  
  for (const scenario of testScenarios) {
    console.log(`\n--- Testing: ${scenario.name} ---`);
    console.log(`Expected: ${scenario.expectedBehavior}`);
    
    try {
      // Simulate the unified export processing
      const result = await simulateUnifiedExport(scenario);
      
      if (result.success) {
        console.log(`✅ PASSED: ${scenario.name}`);
        console.log(`   Output: ${result.outputFile} (${result.fileSize} bytes)`);
        passedTests++;
        
        // Clean up test file
        if (fs.existsSync(result.outputFile)) {
          fs.unlinkSync(result.outputFile);
        }
      } else {
        console.log(`❌ FAILED: ${scenario.name}`);
        console.log(`   Error: ${result.error}`);
        failedTests++;
      }
    } catch (error) {
      console.log(`❌ FAILED: ${scenario.name}`);
      console.log(`   Exception: ${error.message}`);
      failedTests++;
    }
  }
  
  console.log(`\n🎯 Test Summary`);
  console.log(`===============`);
  console.log(`✅ Passed: ${passedTests}`);
  console.log(`❌ Failed: ${failedTests}`);
  console.log(`📊 Success Rate: ${Math.round((passedTests / (passedTests + failedTests)) * 100)}%`);
  
  if (failedTests === 0) {
    console.log(`🎉 All tests passed! Export stability looks good.`);
  } else {
    console.log(`⚠️ Some tests failed. Check the errors above for details.`);
  }
}

// Simulate the unified export processing
async function simulateUnifiedExport(scenario) {
  const { images, transitions, frameDurations, format } = scenario;
  
  try {
    // Generate input flags
    const inputFlags = [];
    images.forEach((img, index) => {
      const duration = (frameDurations[index] || 1000) / 1000;
      inputFlags.push(`-loop 1 -t ${duration} -i "${img.path}"`);
    });
    
    // Generate scale filters
    const targetResolution = { width: 480, height: 480 }; // Smaller resolution for faster testing
    const scaleFilters = images.map((_, index) =>
      `[${index}:v]scale=${targetResolution.width}:${targetResolution.height}:force_original_aspect_ratio=decrease,pad=${targetResolution.width}:${targetResolution.height}:(ow-iw)/2:(oh-ih)/2[v${index}]`
    );
    
    // Build filter complex (simplified version)
    let complexFilterArray = [...scaleFilters];
    
    if (images.length === 1) {
      // Single image
      complexFilterArray.push('[v0]null[out]');
    } else if (transitions.length === 0 || transitions.every(t => !t || t.type === 'cut' || t.type === 'none')) {
      // No transitions - use concat
      const concatInputs = images.map((_, index) => `[v${index}]`).join('');
      complexFilterArray.push(`${concatInputs}concat=n=${images.length}:v=1:a=0[out]`);
    } else {
      // With transitions - simplified xfade chain
      let lastOutput = '[v0]';
      let totalTime = 0;
      
      for (let i = 0; i < images.length - 1; i++) {
        const frameDuration = (frameDurations[i] || 1000) / 1000;
        const transition = transitions[i] || { type: 'fade', duration: 500 };
        const transDuration = Math.max((transition.duration || 500) / 1000, 0.1);
        const transType = transition.type === 'cut' || transition.type === 'none' ? 'fade' : transition.type;
        
        const nextInput = `[v${i + 1}]`;
        const outputLabel = i === images.length - 2 ? '[out]' : `[t${i}]`;
        const offset = totalTime + frameDuration - transDuration;
        
        const xfadeFilter = `${lastOutput}${nextInput}xfade=transition=${transType}:duration=${transDuration}:offset=${offset}${outputLabel}`;
        complexFilterArray.push(xfadeFilter);
        
        lastOutput = outputLabel;
        totalTime += frameDuration;
      }
    }
    
    // Handle GIF processing
    if (format === 'gif') {
      complexFilterArray.push('[out]split[s0][s1]');
      complexFilterArray.push('[s0]palettegen[p]');
      complexFilterArray.push('[s1][p]paletteuse[outgif]');
    }
    
    const filterComplex = complexFilterArray.join(';');
    
    // Generate output options
    let outputOptions;
    if (format === 'gif') {
      outputOptions = `-map "[outgif]" -r 10 -loop 0`;
    } else {
      outputOptions = `-map "[out]" -c:v libx264 -preset ultrafast -crf 28 -pix_fmt yuv420p -r 15`;
    }
    
    // Generate output file
    const outputFile = `/tmp/test_stability_${Date.now()}.${format}`;
    
    // Build final command
    const ffmpegCmd = `ffmpeg ${inputFlags.join(' ')} -filter_complex "${filterComplex}" ${outputOptions} -y "${outputFile}"`;
    
    console.log(`   Command: ffmpeg [inputs] -filter_complex "[${complexFilterArray.length} filters]" [output]`);
    
    // Execute command with timeout
    const { stdout, stderr } = await execAsync(ffmpegCmd, { timeout: 30000 }); // 30 second timeout
    
    // Check if output file was created
    if (fs.existsSync(outputFile)) {
      const stats = fs.statSync(outputFile);
      return {
        success: true,
        outputFile: outputFile,
        fileSize: stats.size
      };
    } else {
      return {
        success: false,
        error: 'Output file was not created'
      };
    }
    
  } catch (error) {
    return {
      success: false,
      error: error.message,
      stderr: error.stderr
    };
  }
}

// Main execution
async function main() {
  try {
    await testExportStability();
  } catch (error) {
    console.error('Test suite failed:', error);
  }
}

main();
