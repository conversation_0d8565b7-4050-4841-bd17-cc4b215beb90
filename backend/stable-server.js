// Stable server with comprehensive health checks
console.log('🔧 Starting stable AnimaGen server...');

require('dotenv').config();
const express = require('express');
const cors = require('cors');
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const os = require('os');

const app = express();

// Health status tracking
let healthStatus = {
  server: 'starting',
  redis: 'disabled',
  ffmpeg: 'unknown',
  disk: 'unknown',
  memory: 'unknown',
  startTime: new Date().toISOString(),
  uptime: 0,
  requestCount: 0,
  errorCount: 0
};

// Middleware setup
app.use(cors({
  origin: ["http://localhost:5173", "http://localhost:5174", "http://localhost:5175"],
  methods: ["GET", "POST", "OPTIONS"],
  credentials: true
}));

app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true, limit: '50mb' }));

// Request counter middleware
app.use((req, res, next) => {
  healthStatus.requestCount++;
  healthStatus.uptime = Math.floor((Date.now() - new Date(healthStatus.startTime)) / 1000);
  next();
});

// Error counter middleware
app.use((err, req, res, next) => {
  healthStatus.errorCount++;
  console.error('❌ Request error:', err.message);
  res.status(500).json({ error: 'Internal server error', message: err.message });
});

// Ensure temp directories exist
const tempDir = path.join(__dirname, 'temp');
const uploadsDir = path.join(__dirname, 'uploads');
const outputDir = path.join(__dirname, 'output');

[tempDir, uploadsDir, outputDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  }
});

// Multer configuration with error handling
const upload = multer({
  dest: tempDir,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB
    files: 50
  },
  fileFilter: (req, file, cb) => {
    const allowedTypes = /jpeg|jpg|png|gif|bmp|webp|tiff/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);

    if (extname && mimetype) {
      cb(null, true);
    } else {
      cb(new Error('Only image files are allowed'));
    }
  }
});

// Health check functions
function checkDiskSpace() {
  try {
    const stats = fs.statSync(__dirname);
    const free = stats.free || 0;
    const total = stats.size || 1;
    const usedPercent = ((total - free) / total) * 100;
    
    return {
      status: usedPercent > 90 ? 'critical' : usedPercent > 80 ? 'warning' : 'ok',
      freeGB: Math.round(free / (1024 * 1024 * 1024)),
      usedPercent: Math.round(usedPercent)
    };
  } catch (error) {
    return { status: 'error', message: error.message };
  }
}

function checkMemory() {
  const used = process.memoryUsage();
  const total = os.totalmem();
  const free = os.freemem();
  const usedPercent = ((total - free) / total) * 100;
  
  return {
    status: usedPercent > 90 ? 'critical' : usedPercent > 80 ? 'warning' : 'ok',
    heapUsedMB: Math.round(used.heapUsed / 1024 / 1024),
    heapTotalMB: Math.round(used.heapTotal / 1024 / 1024),
    systemUsedPercent: Math.round(usedPercent),
    rss: Math.round(used.rss / 1024 / 1024)
  };
}

function checkFFmpeg() {
  return new Promise((resolve) => {
    const { spawn } = require('child_process');
    const ffmpeg = spawn('ffmpeg', ['-version']);
    
    let output = '';
    ffmpeg.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    ffmpeg.on('close', (code) => {
      if (code === 0 && output.includes('ffmpeg version')) {
        const version = output.split('\n')[0].match(/ffmpeg version ([^\s]+)/);
        resolve({
          status: 'ok',
          version: version ? version[1] : 'unknown',
          available: true
        });
      } else {
        resolve({
          status: 'error',
          available: false,
          message: 'FFmpeg not found or not working'
        });
      }
    });
    
    ffmpeg.on('error', () => {
      resolve({
        status: 'error',
        available: false,
        message: 'FFmpeg not installed'
      });
    });
    
    // Timeout after 5 seconds
    setTimeout(() => {
      ffmpeg.kill();
      resolve({
        status: 'timeout',
        available: false,
        message: 'FFmpeg check timed out'
      });
    }, 5000);
  });
}

// Routes

// Comprehensive health check
app.get('/api/health', async (req, res) => {
  try {
    const disk = checkDiskSpace();
    const memory = checkMemory();
    const ffmpeg = await checkFFmpeg();
    
    // Update health status
    healthStatus.disk = disk.status;
    healthStatus.memory = memory.status;
    healthStatus.ffmpeg = ffmpeg.status;
    healthStatus.server = 'running';
    
    const overallStatus = [disk.status, memory.status, ffmpeg.status].includes('critical') 
      ? 'critical' 
      : [disk.status, memory.status, ffmpeg.status].includes('warning')
        ? 'warning'
        : 'ok';
    
    res.json({
      status: overallStatus,
      timestamp: new Date().toISOString(),
      uptime: healthStatus.uptime,
      server: {
        status: 'running',
        requestCount: healthStatus.requestCount,
        errorCount: healthStatus.errorCount,
        nodeVersion: process.version,
        platform: os.platform(),
        arch: os.arch()
      },
      disk,
      memory,
      ffmpeg,
      redis: { status: 'disabled', message: 'Running without Redis for stability' },
      queue: { status: 'disabled', message: 'Queue processing disabled' }
    });
  } catch (error) {
    healthStatus.errorCount++;
    res.status(500).json({
      status: 'error',
      message: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

// Simple health check for load balancers
app.get('/api/ping', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    uptime: healthStatus.uptime
  });
});

// Upload endpoint
app.post('/upload', upload.array('images', 50), (req, res) => {
  try {
    console.log(`📤 Upload request - Session: ${req.query.sessionId}, Files: ${req.files?.length || 0}`);
    
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({
        success: false,
        error: 'No files uploaded'
      });
    }
    
    const processedFiles = req.files.map(file => ({
      originalname: file.originalname,
      filename: file.filename,
      size: file.size,
      mimetype: file.mimetype,
      path: file.path
    }));
    
    res.json({
      success: true,
      message: 'Files uploaded successfully (processing in stable mode)',
      sessionId: req.query.sessionId,
      fileCount: req.files.length,
      files: processedFiles,
      note: 'Server running in stable mode - queue processing disabled'
    });
  } catch (error) {
    healthStatus.errorCount++;
    console.error('❌ Upload error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    app: 'AnimaGen Backend',
    version: '1.0.0-stable',
    status: 'running',
    mode: 'stable',
    uptime: healthStatus.uptime,
    endpoints: {
      health: '/api/health',
      ping: '/api/ping',
      upload: '/upload',
      root: '/'
    },
    features: {
      upload: 'enabled',
      processing: 'disabled',
      queue: 'disabled',
      redis: 'disabled'
    }
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    error: 'Endpoint not found',
    available: ['/', '/api/health', '/api/ping', '/upload']
  });
});

// Start server
const PORT = process.env.PORT || 3001;

console.log('🌐 Starting HTTP server...');

const server = app.listen(PORT, '0.0.0.0', (err) => {
  if (err) {
    console.error('❌ Failed to start server:', err);
    process.exit(1);
  }
  
  healthStatus.server = 'running';
  console.log(`✅ Stable AnimaGen server running on port ${PORT}`);
  console.log(`🌐 Health check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Status: http://localhost:${PORT}/api/ping`);
  console.log(`📤 Upload: http://localhost:${PORT}/upload`);
  console.log(`🏠 Root: http://localhost:${PORT}/`);
});

// Graceful shutdown handling
function gracefulShutdown(signal) {
  console.log(`\n🛑 ${signal} received, starting graceful shutdown...`);
  healthStatus.server = 'shutting_down';
  
  server.close((err) => {
    if (err) {
      console.error('❌ Error during server shutdown:', err);
      process.exit(1);
    }
    
    console.log('✅ Server closed gracefully');
    process.exit(0);
  });
  
  // Force shutdown after 10 seconds
  setTimeout(() => {
    console.log('⚠️ Forced shutdown after timeout');
    process.exit(1);
  }, 10000);
}

process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  healthStatus.errorCount++;
  healthStatus.server = 'error';
  
  // Don't exit in development, but log the error
  if (process.env.NODE_ENV === 'production') {
    gracefulShutdown('UNCAUGHT_EXCEPTION');
  }
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  healthStatus.errorCount++;
  
  // Don't exit in development, but log the error
  if (process.env.NODE_ENV === 'production') {
    gracefulShutdown('UNHANDLED_REJECTION');
  }
});