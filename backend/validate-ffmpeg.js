// FFmpeg validation and configuration checker
const ffmpeg = require('fluent-ffmpeg');
const path = require('path');
const fs = require('fs');

async function validateFFmpeg() {
  console.log('🔧 Validating FFmpeg configuration...');
  
  try {
    // Test 1: Basic FFmpeg availability
    await new Promise((resolve, reject) => {
      ffmpeg.getAvailableFormats((err, formats) => {
        if (err) reject(err);
        else {
          console.log('✅ FFmpeg is available');
          console.log(`📊 Supported formats: ${Object.keys(formats).length}`);
          resolve(formats);
        }
      });
    });

    // Test 2: Check required codecs
    await new Promise((resolve, reject) => {
      ffmpeg.getAvailableCodecs((err, codecs) => {
        if (err) reject(err);
        else {
          const required = ['libx264', 'libvpx-vp9', 'libopus', 'aac'];
          const available = Object.keys(codecs);
          
          console.log('🎬 Codec availability:');
          required.forEach(codec => {
            const hasCodec = available.includes(codec);
            console.log(`  ${codec}: ${hasCodec ? '✅' : '❌'}`);
          });
          
          resolve(codecs);
        }
      });
    });

    // Test 3: Check filters (for transitions)
    await new Promise((resolve, reject) => {
      ffmpeg.getAvailableFilters((err, filters) => {
        if (err) reject(err);
        else {
          const required = ['xfade', 'scale', 'pad', 'concat', 'fps'];
          const available = Object.keys(filters);
          
          console.log('🎨 Filter availability:');
          required.forEach(filter => {
            const hasFilter = available.includes(filter);
            console.log(`  ${filter}: ${hasFilter ? '✅' : '❌'}`);
          });
          
          resolve(filters);
        }
      });
    });

    // Test 4: Directory permissions
    const dirs = ['output', 'uploads', 'logs'];
    console.log('📁 Directory permissions:');
    dirs.forEach(dir => {
      const dirPath = path.join(__dirname, dir);
      try {
        if (!fs.existsSync(dirPath)) {
          fs.mkdirSync(dirPath, { recursive: true });
        }
        
        // Test write permission
        const testFile = path.join(dirPath, 'test-write.tmp');
        fs.writeFileSync(testFile, 'test');
        fs.unlinkSync(testFile);
        
        console.log(`  ${dir}: ✅ Read/Write OK`);
      } catch (error) {
        console.log(`  ${dir}: ❌ ${error.message}`);
      }
    });

    console.log('\n🎉 FFmpeg validation completed successfully!');
    return true;

  } catch (error) {
    console.error('❌ FFmpeg validation failed:', error.message);
    return false;
  }
}

// Run validation if called directly
if (require.main === module) {
  validateFFmpeg().then(success => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = validateFFmpeg;