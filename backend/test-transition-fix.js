#!/usr/bin/env node

/**
 * Test script to verify transition processing consistency between preview and export
 */

const path = require('path');
const fs = require('fs');

// Test data
const testTransitions = [
  { type: 'fade', duration: 500 },
  { type: 'slideleft', duration: 600 },
  { type: 'wipeup', duration: 700 },
  { type: 'circleopen', duration: 800 },
  { type: 'zoomin', duration: 400 },
  { type: 'slide', duration: 500 }, // Legacy
  { type: 'zoom', duration: 600 }   // Legacy
];

const testFrameDurations = [1000, 1500, 2000, 1200];
const testImages = [
  { filename: 'test1.jpg' },
  { filename: 'test2.jpg' },
  { filename: 'test3.jpg' },
  { filename: 'test4.jpg' }
];

console.log('🧪 Testing transition processing consistency...\n');

// Test 1: Verify transition effects mapping
console.log('📋 Test 1: Transition Effects Mapping');
console.log('=====================================');

// Import the transition effects from unified-export
const unifiedExportPath = path.join(__dirname, 'routes', 'unified-export.js');
const unifiedExportContent = fs.readFileSync(unifiedExportPath, 'utf8');

// Extract transition effects mapping
const transitionEffectsMatch = unifiedExportContent.match(/const transitionEffects = \{([\s\S]*?)\};/);
if (transitionEffectsMatch) {
  console.log('✅ Transition effects mapping found in unified-export.js');
  
  // Check for key transition types
  const mappingContent = transitionEffectsMatch[1];
  const requiredTypes = ['fade', 'slideleft', 'wipeup', 'circleopen', 'zoomin', 'slide', 'zoom'];
  
  for (const type of requiredTypes) {
    if (mappingContent.includes(`${type}:`)) {
      console.log(`  ✅ ${type}: mapped`);
    } else {
      console.log(`  ❌ ${type}: NOT mapped`);
    }
  }
} else {
  console.log('❌ Transition effects mapping NOT found in unified-export.js');
}

console.log('\n📋 Test 2: buildUnifiedTransitionChain Function');
console.log('===============================================');

// Check if buildUnifiedTransitionChain function exists
if (unifiedExportContent.includes('function buildUnifiedTransitionChain')) {
  console.log('✅ buildUnifiedTransitionChain function found in unified-export.js');
} else {
  console.log('❌ buildUnifiedTransitionChain function NOT found in unified-export.js');
}

// Check if the function is being used in the processing logic
if (unifiedExportContent.includes('buildUnifiedTransitionChain(')) {
  console.log('✅ buildUnifiedTransitionChain function is being called');
} else {
  console.log('❌ buildUnifiedTransitionChain function is NOT being called');
}

console.log('\n📋 Test 3: Consistency Check');
console.log('=============================');

// Compare with main index.js
const indexPath = path.join(__dirname, 'index.js');
const indexContent = fs.readFileSync(indexPath, 'utf8');

const indexTransitionEffectsMatch = indexContent.match(/const transitionEffects = \{([\s\S]*?)\};/);
if (indexTransitionEffectsMatch && transitionEffectsMatch) {
  const indexMapping = indexTransitionEffectsMatch[1];
  const unifiedMapping = transitionEffectsMatch[1];
  
  // Simple consistency check
  const indexTypes = (indexMapping.match(/\w+:/g) || []).map(m => m.replace(':', ''));
  const unifiedTypes = (unifiedMapping.match(/\w+:/g) || []).map(m => m.replace(':', ''));
  
  console.log(`Index.js has ${indexTypes.length} transition mappings`);
  console.log(`Unified-export.js has ${unifiedTypes.length} transition mappings`);
  
  if (indexTypes.length === unifiedTypes.length) {
    console.log('✅ Mapping counts match');
  } else {
    console.log('⚠️  Mapping counts differ - this may be expected');
  }
} else {
  console.log('❌ Could not compare transition mappings');
}

console.log('\n📋 Test 4: API Endpoint Test Data');
console.log('==================================');

// Generate test payload for API testing
const testPayload = {
  images: testImages,
  transitions: testTransitions.slice(0, testImages.length - 1), // n-1 transitions for n images
  frameDurations: testFrameDurations,
  sessionId: 'test_session_' + Date.now(),
  format: 'mp4',
  fps: 30,
  quality: 'standard'
};

console.log('Test payload for API testing:');
console.log(JSON.stringify(testPayload, null, 2));

console.log('\n🎯 Summary');
console.log('==========');
console.log('The transition processing fix should ensure that:');
console.log('1. ✅ Direct export uses the same transition logic as preview');
console.log('2. ✅ All frontend transition types are properly mapped');
console.log('3. ✅ Legacy transition types (slide, zoom) are supported');
console.log('4. ✅ Both GIF and video exports use robust transition processing');
console.log('\nTo test manually:');
console.log('1. Create a slideshow with various transition types');
console.log('2. Generate a preview and verify transitions work');
console.log('3. Export the final video/GIF and verify transitions match the preview');
console.log('4. Test with ≤3 images (direct processing) and >3 images (worker processing)');
