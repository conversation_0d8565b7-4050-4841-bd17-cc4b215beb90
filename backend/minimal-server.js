// Minimal server without any dependencies
const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
  });
  
  if (req.method === 'OPTIONS') {
    res.end();
    return;
  }
  
  if (req.url === '/api/health') {
    res.end(JSON.stringify({ status: 'ok', message: 'Minimal server running' }));
  } else if (req.url.startsWith('/upload')) {
    res.end(JSON.stringify({ success: true, message: 'Upload endpoint' }));
  } else {
    res.end(JSON.stringify({ error: 'Not found' }));
  }
});

server.listen(3001, '0.0.0.0', () => {
  console.log('✅ Minimal server running on port 3001');
});

process.on('SIGTERM', () => {
  console.log('🛑 Shutting down server...');
  server.close();
});