#!/usr/bin/env node

/**
 * Test script completo para probar el nuevo flujo Preview -> Export
 * Incluye subida de imágenes reales y prueba de conversión
 */

const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

const API_BASE_URL = 'http://localhost:3001';

// Helper function to make HTTP requests
async function makeRequest(method, endpoint, data = null, isFormData = false) {
  const url = `${API_BASE_URL}${endpoint}`;
  const options = {
    method,
  };

  if (data) {
    if (isFormData) {
      options.body = data;
    } else {
      options.headers = { 'Content-Type': 'application/json' };
      options.body = JSON.stringify(data);
    }
  } else if (!isFormData) {
    options.headers = { 'Content-Type': 'application/json' };
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return {
      status: response.status,
      data: result,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      data: { error: error.message },
      ok: false
    };
  }
}

// Create simple test images (1x1 PNG)
function createTestPNG(color = [255, 0, 0]) {
  // Create a minimal 1x1 PNG
  const [r, g, b] = color;
  const png = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x01, // width: 1
    0x00, 0x00, 0x00, 0x01, // height: 1
    0x08, 0x02, 0x00, 0x00, 0x00, // bit depth: 8, color type: 2 (RGB), compression: 0, filter: 0, interlace: 0
    0x90, 0x77, 0x53, 0xDE, // IHDR CRC
    0x00, 0x00, 0x00, 0x0C, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x08, 0x99, 0x01, 0x01, 0x00, 0x00, 0x00, 0x03, r, g, b, 0x00, 0x02, // compressed RGB data
    0x7E, 0x05, 0x27, 0xDB, // IDAT CRC (placeholder)
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // IEND CRC
  ]);
  return png;
}

async function testCompletePreviewExportFlow() {
  console.log('🧪 Testing Complete Preview -> Export Flow');
  console.log('==========================================\n');

  const sessionId = `test_complete_${Date.now()}`;
  
  console.log(`📋 Session ID: ${sessionId}\n`);

  // Step 1: Upload test images
  console.log('📤 Step 1: Uploading test images...');
  
  const formData = new FormData();
  
  // Load and add test images
  const image1Path = path.join(__dirname, 'test-assets', 'red.png');
  const image2Path = path.join(__dirname, 'test-assets', 'green.png');

  if (!fs.existsSync(image1Path) || !fs.existsSync(image2Path)) {
    console.log('❌ Test images not found. Creating them...');
    console.log('   Run: magick -size 800x600 xc:red test-assets/red.png');
    console.log('   Run: magick -size 800x600 xc:green test-assets/green.png');
    return;
  }

  const image1 = fs.createReadStream(image1Path);
  const image2 = fs.createReadStream(image2Path);

  formData.append('images', image1, 'red.png');
  formData.append('images', image2, 'green.png');

  const uploadResponse = await makeRequest('POST', `/upload?sessionId=${sessionId}`, formData, true);
  console.log(`   Status: ${uploadResponse.status}`);
  
  if (!uploadResponse.ok) {
    console.log('❌ Upload failed:');
    console.log(`   Error: ${JSON.stringify(uploadResponse.data, null, 2)}`);
    return;
  }

  console.log('✅ Images uploaded successfully!');
  console.log(`   Files: ${uploadResponse.data.files.map(f => f.filename).join(', ')}\n`);
  
  const uploadedFiles = uploadResponse.data.files;

  // Step 2: Generate high-quality preview
  console.log('🎬 Step 2: Generating high-quality preview...');
  const previewData = {
    images: uploadedFiles.map(f => ({ filename: f.filename })),
    transitions: [{ type: 'fade', duration: 500 }],
    frameDurations: [2000, 2000],
    sessionId: sessionId
  };

  const previewResponse = await makeRequest('POST', '/preview', previewData);
  console.log(`   Status: ${previewResponse.status}`);
  
  if (!previewResponse.ok) {
    console.log('❌ Preview generation failed:');
    console.log(`   Error: ${JSON.stringify(previewResponse.data, null, 2)}`);
    return;
  }

  console.log('✅ High-quality preview generated!');
  console.log(`   Preview URL: ${previewResponse.data.previewUrl}`);
  console.log(`   Preview filename: ${previewResponse.data.filename}\n`);
  
  const previewFilename = previewResponse.data.filename;

  // Step 3: Test conversion to different formats
  console.log('🔄 Step 3: Testing format conversions...');
  const formats = [
    { format: 'mp4', quality: 'high' },
    { format: 'webm', quality: 'standard' },
    { format: 'mov', quality: 'ultra' },
    { format: 'gif', quality: 'standard' }
  ];

  const results = [];

  for (const { format, quality } of formats) {
    console.log(`\n   🎯 Converting to ${format.toUpperCase()} (${quality})...`);
    
    const exportData = {
      previewFilename: previewFilename,
      format: format,
      quality: quality,
      sessionId: sessionId
    };

    const exportResponse = await makeRequest('POST', '/export/from-preview', exportData);
    console.log(`      Status: ${exportResponse.status}`);
    
    if (exportResponse.ok) {
      console.log(`      ✅ ${format.toUpperCase()} export completed!`);
      console.log(`      📁 Filename: ${exportResponse.data.filename}`);
      console.log(`      🔗 Download: ${exportResponse.data.downloadUrl}`);
      
      results.push({
        format,
        quality,
        success: true,
        filename: exportResponse.data.filename,
        downloadUrl: exportResponse.data.downloadUrl
      });
    } else {
      console.log(`      ❌ ${format.toUpperCase()} export failed:`);
      console.log(`      Error: ${exportResponse.data.error}`);
      
      results.push({
        format,
        quality,
        success: false,
        error: exportResponse.data.error
      });
    }
  }

  // Step 4: Summary
  console.log('\n📊 Step 4: Results Summary');
  console.log('===========================');
  
  const successful = results.filter(r => r.success);
  const failed = results.filter(r => !r.success);
  
  console.log(`✅ Successful exports: ${successful.length}/${results.length}`);
  successful.forEach(r => {
    console.log(`   - ${r.format.toUpperCase()} (${r.quality}): ${r.filename}`);
  });
  
  if (failed.length > 0) {
    console.log(`❌ Failed exports: ${failed.length}/${results.length}`);
    failed.forEach(r => {
      console.log(`   - ${r.format.toUpperCase()} (${r.quality}): ${r.error}`);
    });
  }

  console.log('\n🎯 Quality Improvements Applied:');
  console.log('   ✅ Preview resolution: Up to 1920x1080 (was 1280x720)');
  console.log('   ✅ Preview CRF: 18 (was 23) - Higher quality');
  console.log('   ✅ Preview bitrate: 5M (was 2M) - Better quality');
  console.log('   ✅ Preview preset: medium (was ultrafast) - Better compression');
  console.log('   ✅ Preview profile: high (was baseline) - Better efficiency');

  console.log('\n🚀 Workflow Benefits:');
  console.log('   ✅ Single source of truth (preview MP4)');
  console.log('   ✅ Consistent quality across all formats');
  console.log('   ✅ Faster exports (no re-processing from images)');
  console.log('   ✅ Eliminates preview vs export discrepancies');

  console.log('\n✅ Complete Preview -> Export flow test finished!');
}

// Run the test
if (require.main === module) {
  testCompletePreviewExportFlow().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testCompletePreviewExportFlow };
