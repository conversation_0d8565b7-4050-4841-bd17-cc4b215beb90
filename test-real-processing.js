// Test script for real video processing
const API_BASE = 'http://localhost:3001';

async function testRealProcessing() {
  console.log('🧪 Testing real video processing...');
  
  try {
    // Test 1: Health check
    console.log('1️⃣ Checking server health...');
    const healthResponse = await fetch(`${API_BASE}/api/health`);
    const health = await healthResponse.json();
    console.log('Health:', health.status);
    
    // Test 2: API status
    console.log('2️⃣ Checking API status...');
    const statusResponse = await fetch(`${API_BASE}/api/status`);
    const status = await statusResponse.json();
    console.log('Server mode:', status.jobQueue?.enabled ? 'Full Processing' : 'Sync Processing');
    
    // Test 3: Check if we have test images
    console.log('3️⃣ Looking for test session...');
    
    // Note: For a real test, you would:
    // 1. Upload test images first
    // 2. Get the sessionId from the upload response
    // 3. Then test preview generation with real data
    
    console.log('✅ Basic connectivity tests passed!');
    console.log('📝 To complete the test:');
    console.log('   1. Upload some images via the frontend');
    console.log('   2. Try generating a preview');
    console.log('   3. Check the console for processing logs');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// For browser console execution
window.testRealProcessing = testRealProcessing;