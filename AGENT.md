# AnimaGen Project

AnimaGen is a professional video editor focused on simplicity and power. It allows editing multiple videos with trim, split, transitions and effects, maintaining the ease of use of the original slideshow but with advanced video capabilities.

## Project Overview

- **Frontend**: React + TypeScript + Vite + Tailwind CSS
- **Backend**: Express.js + Socket.IO + FFmpeg
- **Processing**: FFmpeg for video/gif generation
- **Real-time**: WebSocket progress tracking

## Build & Commands

### Development Setup

```bash
# Backend
cd backend && npm install && npm start  # Runs on :3001

# Frontend  
cd frontend && npm install && npm run dev  # Runs on :5173
```

### Production Deployment

```bash
# Using PM2 for backend
cd backend
npm run pm2:start  # Start with PM2
npm run pm2:stop   # Stop PM2 service
npm run pm2:status # Check status
npm run pm2:logs   # View logs
```

## Project Structure

- `frontend/` - React SPA (deployable to Vercel)
- `backend/` - Express API + FFmpeg processing
  - `temp/` - Temporary uploaded files
  - `output/` - Processed video/GIF files

## Core Features

- **Multi-format Export**: GIF, MP4, WebM, MOV
- **Timeline Editor**: Drag & drop with custom transitions
- **Real-time Preview**: Live preview with progress tracking
- **Video Management**: Multi-format support (MP4, WebM, MOV)
- **Editing Tools**: Trim, split, speed control, sequence management
- **Transitions**: Crossfade, slide, zoom, wipe, dissolve

## Development Guidelines

### Backend

- Use environment variables for configuration (see `.env` file)
- Implement proper error handling and logging
- Use the job queue system for processing large files
- Clean up temporary files after processing

### Frontend

- Follow the refactoring plan in `REFACTOR_PLAN.md`
- Maintain separation of concerns in components
- Use specialized hooks for specific functionality
- Implement builder pattern for complex components

## Testing

- Automated browser testing for UI validation
- API endpoint testing with curl commands
- Performance testing for large video files
- Export quality validation

## Security Considerations

- Implement rate limiting for API endpoints
- Add authentication for production deployment
- Validate file types and sizes
- Set appropriate CORS policies

## Configuration

- Backend configuration via `.env` file
- Feature flags for enabling/disabling functionality
- Redis configuration for job queue system
- FFmpeg path configuration

## Development Roadmap

See `ANIMAGEN_SPECIFICATIONS.md` for the detailed development plan, which includes:

- Phase 1: Foundation & Multi-Video
- Phase 2: Advanced Editing & Effects
- Phase 3: Production Ready

## Success Metrics

- Support videos up to 1GB
- Real-time preview for sequences up to 10 minutes
- Export speed: <2x real-time for 1080p
- Timeline responsiveness: <100ms for all interactions