#!/usr/bin/env node

const http = require('http');

// Test data for unified export with different image counts
function createTestData(imageCount) {
  const images = [];
  const transitions = [];
  const frameDurations = [];

  // Create test images
  for (let i = 0; i < imageCount; i++) {
    images.push({ filename: `frame${i + 1}.png` });
    frameDurations.push(1000);

    // Add transitions (one less than image count)
    if (i < imageCount - 1) {
      transitions.push({ type: 'fade', duration: 0.5 });
    }
  }

  return {
    images,
    transitions,
    frameDurations,
    sessionId: 'test_session_' + Date.now(),
    fps: 30,
    quality: 'standard',
    resolution: '1080p'
  };
}

console.log('🧪 Testing unified export endpoint...');

// Test export with specific image count and format
function testExport(format, imageCount) {
  return new Promise((resolve, reject) => {
    const testData = createTestData(imageCount);
    const postData = JSON.stringify(testData);
    
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: `/api/unified-export/${format}`,
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData)
      }
    };

    console.log(`\n🎬 Testing ${format.toUpperCase()} export with ${imageCount} images...`);
    
    const req = http.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        console.log(`📥 Response status: ${res.statusCode}`);
        console.log(`📥 Response headers:`, res.headers);
        
        try {
          const response = JSON.parse(data);
          console.log(`📥 Response body:`, JSON.stringify(response, null, 2));
          
          if (response.success) {
            console.log(`✅ ${format.toUpperCase()} export successful!`);
            if (response.jobId) {
              console.log(`🔍 Job ID: ${response.jobId}`);
              console.log(`📊 Status URL: ${response.statusUrl}`);
              console.log(`⬇️ Download URL: ${response.downloadUrl}`);
              
              // Test status endpoint
              setTimeout(() => {
                testStatus(response.jobId, format);
              }, 1000);
            }
          } else {
            console.log(`❌ ${format.toUpperCase()} export failed:`, response.error);
          }
          
          resolve(response);
        } catch (error) {
          console.log(`❌ Failed to parse response:`, error.message);
          console.log(`📥 Raw response:`, data);
          reject(error);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Request error:`, error.message);
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

function testStatus(jobId, format) {
  const options = {
    hostname: 'localhost',
    port: 3001,
    path: `/api/export/status/${jobId}`,
    method: 'GET'
  };

  console.log(`\n🔍 Testing status for ${format.toUpperCase()} job: ${jobId}`);
  
  const req = http.request(options, (res) => {
    let data = '';
    
    res.on('data', (chunk) => {
      data += chunk;
    });
    
    res.on('end', () => {
      console.log(`📊 Status response: ${res.statusCode}`);
      
      try {
        const response = JSON.parse(data);
        console.log(`📊 Status data:`, JSON.stringify(response, null, 2));
        
        if (response.success) {
          console.log(`✅ Status check successful for ${format.toUpperCase()}!`);
        } else {
          console.log(`❌ Status check failed for ${format.toUpperCase()}:`, response.error);
        }
      } catch (error) {
        console.log(`❌ Failed to parse status response:`, error.message);
        console.log(`📊 Raw status response:`, data);
      }
    });
  });

  req.on('error', (error) => {
    console.log(`❌ Status request error:`, error.message);
  });

  req.end();
}

// Run comprehensive tests with different image counts
async function runTests() {
  try {
    console.log('🚀 Starting comprehensive unified export tests...\n');
    console.log('📋 Testing direct processing with various image counts to verify scalability\n');

    const testCases = [
      { format: 'mp4', imageCount: 3, description: 'Small slideshow (3 images)' },
      { format: 'mp4', imageCount: 5, description: 'Medium slideshow (5 images)' },
      { format: 'mp4', imageCount: 10, description: 'Large slideshow (10 images)' },
      { format: 'gif', imageCount: 3, description: 'Small GIF (3 images)' },
      { format: 'gif', imageCount: 7, description: 'Medium GIF (7 images)' }
    ];

    for (let i = 0; i < testCases.length; i++) {
      const testCase = testCases[i];
      console.log(`\n📝 Test ${i + 1}/${testCases.length}: ${testCase.description}`);

      try {
        await testExport(testCase.format, testCase.imageCount);
        console.log(`✅ Test ${i + 1} passed: ${testCase.description}`);
      } catch (error) {
        console.log(`❌ Test ${i + 1} failed: ${testCase.description} - ${error.message}`);
      }

      // Wait between tests
      if (i < testCases.length - 1) {
        console.log('⏳ Waiting 3 seconds before next test...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    }

    console.log('\n🏁 All tests completed!');
    console.log('\n📊 Summary:');
    console.log('- Direct processing should now work for any number of images');
    console.log('- Queue processing has been eliminated to prevent 80% completion failures');
    console.log('- All formats (MP4, GIF, WebM, MOV) use the same reliable direct processing path');

  } catch (error) {
    console.error('💥 Test suite failed:', error);
  }
}

runTests();
