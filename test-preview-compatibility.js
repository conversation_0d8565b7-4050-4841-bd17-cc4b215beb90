// Test script para validar compatibilidad de preview
// Ejecutar en consola del navegador

async function testPreviewCompatibility() {
  console.log('🧪 Testing preview video compatibility...');
  
  // Test 1: Video format support
  const video = document.createElement('video');
  const formats = {
    'video/mp4; codecs="avc1.42E01E"': 'MP4 H.264 Baseline',
    'video/mp4; codecs="avc1.4D401E"': 'MP4 H.264 Main',
    'video/mp4; codecs="avc1.64001E"': 'MP4 H.264 High',
    'video/webm; codecs="vp9"': 'WebM VP9',
    'video/webm; codecs="vp8"': 'WebM VP8'
  };
  
  console.log('📽️ Format Support:');
  for (const [format, name] of Object.entries(formats)) {
    const support = video.canPlayType(format);
    console.log(`  ${name}: ${support || 'not supported'}`);
  }
  
  // Test 2: CORS and Range support
  try {
    const testUrl = 'http://localhost:3001/api/health';
    const response = await fetch(testUrl);
    const corsHeaders = response.headers.get('access-control-allow-origin');
    console.log('🌐 CORS Headers:', corsHeaders);
  } catch (e) {
    console.error('❌ CORS Test Failed:', e.message);
  }
  
  // Test 3: Mobile/iOS specific
  const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  const isIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
  
  console.log('📱 Device Info:', {
    isMobile,
    isIOS,
    userAgent: navigator.userAgent.substring(0, 50) + '...'
  });
  
  if (isIOS) {
    console.log('🍎 iOS detected - playsInline and user interaction required for autoplay');
  }
  
  // Test 4: Memory constraints
  const memory = (performance as any)?.memory;
  if (memory) {
    console.log('💾 Memory Info:', {
      used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + 'MB',
      total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + 'MB',
      limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
    });
  }
  
  return {
    formats: Object.fromEntries(
      Object.entries(formats).map(([format, name]) => [name, video.canPlayType(format)])
    ),
    isMobile,
    isIOS,
    memoryAvailable: memory ? memory.jsHeapSizeLimit - memory.usedJSHeapSize > 50 * 1024 * 1024 : true
  };
}

// Para ejecutar: testPreviewCompatibility().then(console.log);
window.testPreviewCompatibility = testPreviewCompatibility;