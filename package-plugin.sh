#!/bin/bash

# 🚀 AnimaGen Plugin Packaging Script
# Este script empaqueta el plugin para distribución a beta testers

echo "🎬 Empaquetando AnimaGen para Beta Testing..."

# Verificar que estamos en el directorio correcto
if [ ! -f "figma-plugin/manifest.json" ]; then
    echo "❌ Error: No se encontró el manifest.json. Asegúrate de estar en el directorio raíz del proyecto."
    exit 1
fi

# Crear directorio de distribución si no existe
mkdir -p dist

# Construir el plugin
echo "🔨 Construyendo el plugin..."
cd figma-plugin
npm run build

if [ $? -ne 0 ]; then
    echo "❌ Error: La construcción del plugin falló."
    exit 1
fi

# Crear el paquete ZIP
echo "📦 Creando paquete ZIP..."
cd ..
zip -r "dist/AnimaGen-Beta-v1.0.0.zip" figma-plugin/build figma-plugin/manifest.json BETA_TESTING_GUIDE.md

if [ $? -eq 0 ]; then
    echo "✅ Plugin empaquetado exitosamente!"
    echo "📁 Archivo creado: dist/AnimaGen-Beta-v1.0.0.zip"
    echo ""
    echo "📋 Para distribuir a beta testers:"
    echo "1. Envía el archivo ZIP a los testers"
    echo "2. Incluye el BETA_TESTING_GUIDE.md"
    echo "3. Instruye a los testers a:"
    echo "   - Extraer el ZIP"
    echo "   - Abrir Figma"
    echo "   - Ir a Plugins → Development → Import plugin from manifest..."
    echo "   - Seleccionar el manifest.json"
    echo ""
    echo "🎉 ¡Listo para beta testing!"
else
    echo "❌ Error: No se pudo crear el paquete ZIP."
    exit 1
fi 