#!/usr/bin/env node

/**
 * Test script para verificar que las configuraciones de calidad GIF
 * se aplican correctamente en el nuevo sistema Master → Export
 */

const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001';

// Helper function to make HTTP requests
async function makeRequest(method, endpoint, data = null) {
  const url = `${API_BASE_URL}${endpoint}`;
  const options = {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
  };

  if (data) {
    options.body = JSON.stringify(data);
  }

  try {
    const response = await fetch(url, options);
    const result = await response.json();
    return {
      status: response.status,
      data: result,
      ok: response.ok
    };
  } catch (error) {
    return {
      status: 0,
      data: { error: error.message },
      ok: false
    };
  }
}

// Helper function to get file size
function getFileSize(filePath) {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

// Helper function to format file size
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

async function testGifQualitySettings() {
  console.log('🧪 Testing GIF Quality Settings - Master → Export System');
  console.log('========================================================\n');

  const sessionId = 'test_curl_1752215100'; // Using existing session
  const masterFilename = 'master_test_curl_1752215100_1752224292972.mp4'; // Using existing master

  // Test all quality levels
  const qualityLevels = [
    { quality: 'low', description: '10 FPS, 480px, 64 colors, no dithering' },
    { quality: 'standard', description: '15 FPS, 720px, 128 colors, bayer dithering' },
    { quality: 'high', description: '20 FPS, 1080px, 256 colors, bayer dithering' },
    { quality: 'ultra', description: '25 FPS, 1440px, 256 colors, floyd_steinberg' }
  ];

  console.log(`📋 Test Configuration:`);
  console.log(`   Master file: ${masterFilename}`);
  console.log(`   Session ID: ${sessionId}`);
  console.log(`   Quality levels: ${qualityLevels.length}\n`);

  const results = [];

  for (const { quality, description } of qualityLevels) {
    console.log(`🎨 Testing ${quality.toUpperCase()} quality...`);
    console.log(`   Settings: ${description}`);

    const startTime = Date.now();
    
    const exportResponse = await makeRequest('POST', '/export/from-master', {
      masterFilename,
      format: 'gif',
      quality,
      sessionId
    });

    const duration = Date.now() - startTime;

    if (exportResponse.ok) {
      const filename = exportResponse.data.filename;
      const filePath = path.join(__dirname, 'backend', 'output', filename);
      const fileSize = getFileSize(filePath);
      
      console.log(`   ✅ Export completed in ${duration}ms`);
      console.log(`   📁 File: ${filename}`);
      console.log(`   📊 Size: ${formatFileSize(fileSize)}`);
      console.log(`   🔗 URL: ${API_BASE_URL}${exportResponse.data.downloadUrl}\n`);
      
      results.push({
        quality,
        description,
        success: true,
        filename,
        fileSize,
        duration,
        downloadUrl: exportResponse.data.downloadUrl
      });
    } else {
      console.log(`   ❌ Export failed: ${exportResponse.data.error}\n`);
      
      results.push({
        quality,
        description,
        success: false,
        error: exportResponse.data.error
      });
    }
  }

  // Analysis and comparison
  console.log('📊 Quality Settings Analysis');
  console.log('=============================');
  
  const successful = results.filter(r => r.success);
  
  if (successful.length > 0) {
    console.log('\n🎯 File Size Comparison:');
    successful.forEach((result, index) => {
      const sizeRatio = index === 0 ? 1 : (result.fileSize / successful[0].fileSize);
      console.log(`   ${result.quality.toUpperCase().padEnd(8)} | ${formatFileSize(result.fileSize).padEnd(8)} | ${sizeRatio.toFixed(1)}x size`);
    });
    
    console.log('\n⚡ Performance Comparison:');
    successful.forEach(result => {
      console.log(`   ${result.quality.toUpperCase().padEnd(8)} | ${result.duration}ms processing time`);
    });
    
    console.log('\n🔧 Technical Settings Applied:');
    successful.forEach(result => {
      console.log(`   ${result.quality.toUpperCase().padEnd(8)} | ${result.description}`);
    });
    
    // Quality recommendations
    console.log('\n💡 Quality Recommendations:');
    console.log('   LOW      | Best for: Quick previews, small file size needed');
    console.log('   STANDARD | Best for: General use, good balance of quality/size');
    console.log('   HIGH     | Best for: High-quality sharing, social media');
    console.log('   ULTRA    | Best for: Maximum quality, professional use');
    
    // Verify settings are actually different
    const sizeDifference = successful[successful.length - 1].fileSize / successful[0].fileSize;
    if (sizeDifference > 2) {
      console.log(`\n✅ VERIFICATION PASSED: Quality settings are working correctly!`);
      console.log(`   Size difference between lowest and highest: ${sizeDifference.toFixed(1)}x`);
    } else {
      console.log(`\n❌ VERIFICATION FAILED: Quality settings may not be working properly.`);
      console.log(`   Size difference too small: ${sizeDifference.toFixed(1)}x`);
    }
  }
  
  const failed = results.filter(r => !r.success);
  if (failed.length > 0) {
    console.log('\n❌ Failed Exports:');
    failed.forEach(result => {
      console.log(`   ${result.quality.toUpperCase()}: ${result.error}`);
    });
  }

  console.log('\n✅ GIF Quality Settings test completed!');
}

// Run the test
if (require.main === module) {
  testGifQualitySettings().catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });
}

module.exports = { testGifQualitySettings };
